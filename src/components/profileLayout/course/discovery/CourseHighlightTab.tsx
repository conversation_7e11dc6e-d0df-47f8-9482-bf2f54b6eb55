'use client';
import { CourseInfo, useFavoriteCourse } from '@/features/courses';
import { Button, Carousel, Modal, Space, Typography } from 'antd';
import CourseHighlightItem from 'components/CourseHighlightItem';
import { routePaths } from 'config';
import { useQuerySearch } from 'hooks';
import {
  ConfirmSecondaryIcon,
  NextSlideIcon,
  PrevSlideIcon,
  StarEightEdgeOutlined,
  StarFourEdgeOutlined,
  UnderlineOutlined,
} from 'icons';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { getLabelCourseLevel } from 'utils/convert/course';
import './index.scss';
import Line from './Line';

export type CourseHighlightTabProps = {
  title?: string | ReactNode;
  courses: CourseInfo[];
};

function CourseHighlightTab({ title = 'Khóa học nổi bật', courses }: Readonly<CourseHighlightTabProps>) {
  const queryParams = useQuerySearch();
  const [isClient, setIsClient] = useState(false);
  const carousel = useRef<any>(null);

  const { onFavorite } = useFavoriteCourse();

  const [isShowPopup, setIsShowPopup] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (queryParams?.isShow) {
      setIsShowPopup(true);
    } else {
      setIsShowPopup(false);
    }
  }, []);

  const onClosePopup = () => {
    window.location.href = routePaths.profile.children.course.children.discovery.path;
  };

  const getCourseItems = () =>
    courses?.map((course) => (
      <div
        key={course.id}
        className="scaleItem relative h-[270px] w-full overflow-hidden rounded-[12px] md:m-[56px] md:w-[480px]"
      >
        <CourseHighlightItem
          courseId={course.id}
          description={course.courseDescription}
          title={course.courseName}
          totalChapter={`${course.totalSections ?? 0} chương`}
          courseType={getLabelCourseLevel(Number(course.courseLevelId))}
          urlImg={course.courseThumbnailImage ?? ''}
          amountRegister={Number(course.totalLearner) ?? 0}
          rate={Number(course.avgRating) ?? 0}
          isFavorite={!!course?.isFavorite}
          onLike={(isFavorite: boolean) => onFavorite({ courseId: course.id, isFavorite })}
          author={course.createdBy.name ?? ''}
        />
      </div>
    ));

  return (
    <div className="course-high-light relative rounded-xl bg-primary pb-16">
      <span className={'absolute left-0 top-0 hidden lg:block'}>
        <Line />
      </span>
      <div className="grid grid-cols-3 gap-4 p-6">
        <StarEightEdgeOutlined />
        <div className="flex items-end justify-center text-2xl font-bold text-white lg:pt-[30px]">
          <div className={'flex flex-col gap-0'}>
            {title}
            <UnderlineOutlined />
          </div>
        </div>
        <div className="flex justify-end">
          <StarFourEdgeOutlined />
        </div>
      </div>
      <button className={'absolute left-1 top-1/2 z-10 cursor-pointer'} onClick={() => carousel.current.prev()}>
        <PrevSlideIcon />
      </button>
      <div className={'min-h-[270px] md:min-h-[382px]'}>
        {isClient && (
          <Carousel
            className="center"
            infinite
            dots
            variableWidth
            responsive={[
              {
                breakpoint: 600,
                settings: {
                  slidesToShow: 1,
                  centerPadding: '0px',
                  variableWidth: false,
                  centerMode: false,
                  adaptiveHeight: false,
                  dots: false,
                },
              },
            ]}
            centerPadding={'60px'}
            // arrows
            adaptiveHeight
            centerMode
            speed={1000}
            ref={carousel}
            autoplay
            appendDots={(dots) => {
              return (
                <div
                  style={{
                    borderRadius: '10px',
                    padding: '10px',
                    position: 'absolute',
                    bottom: '-40px',
                  }}
                >
                  <ul style={{ margin: '0px' }}> {dots} </ul>
                </div>
              );
            }}
          >
            {getCourseItems()}
          </Carousel>
        )}
      </div>
      <button className={'absolute right-1 top-1/2 cursor-pointer'} onClick={() => carousel.current.next()}>
        <NextSlideIcon />
      </button>
      <Modal title={null} footer={null} open={isShowPopup} onCancel={onClosePopup}>
        <Space className={'center-x-y w-full p-8 text-center'} direction={'vertical'}>
          <ConfirmSecondaryIcon />
          <Typography.Title level={3}>Chúc mừng!</Typography.Title>
          <p>
            Bạn đã được nâng cấp lên gói tài khoản Nâng cao trong 30 ngày. Bạn có thể ngay lập tức trải nghiệm các khóa
            học độc quyền và tính năng tương tác nội dung học nâng cao ngay bây giờ.
          </p>
          <Button className={'mt-4'} type={'primary'} onClick={onClosePopup}>
            Bắt đầu học với Studify
          </Button>
        </Space>
      </Modal>
    </div>
  );
}

export default CourseHighlightTab;
