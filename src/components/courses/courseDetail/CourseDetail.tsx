import { CourseDetailInfo } from '@/features/courses';
import { getCoursesOfCreator } from '@/features/courses/services/server';
import BreadCrumb from 'components/courses/detail/BreadCrumb';
import BreadCrumbPublic from 'components/courses/detail/BreadCrumbPublic';
import Description from 'components/courses/detail/Description';
import CourseRelatedList from 'components/courses/detail/RelatedList';
import Footer from 'components/Footer';
import ViewMore from 'components/home/<USER>';
import { LearnCourse } from 'components/learn-course';
import { ShortCourse } from 'components/short-course';
import { StandardCourseContent } from 'components/standard-course-content';
import { routePaths } from 'config';
import { CourseType } from 'constants/enum';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import querystring from 'query-string';
import { userServices } from 'services/user.services';

const CourseOtherList = dynamic(() => import('components/courses/detail/OtherList'), {
  ssr: false,
});

const Rating = dynamic(() => import('components/courses/detail/Rating'), {
  ssr: false,
});

export const CourseDetail = async ({ courseInfo }: { courseInfo: CourseDetailInfo }) => {
  const Faq = dynamic(() => import('components/courses/detail/Faq'), {
    ssr: courseInfo.courseFaq && courseInfo.courseFaq.length > 0,
  });

  const { isLoggedIn } = await userServices();

  const { data: relatedCourses } = await getCoursesOfCreator({ userId: courseInfo.createdBy.id, limit: 4 });

  const author = courseInfo?.createdBy;

  const topicId = courseInfo.topic.id;
  const courseTypeId = courseInfo.courseTypeId;

  return (
    <>
      <BreadCrumbPublic
        breadCrumb={
          <BreadCrumb
            prePageUrl={
              isLoggedIn ? routePaths.profile.children.course.children.discovery.path : routePaths.course.index
            }
            categoryCourseHref={
              isLoggedIn
                ? querystring.stringifyUrl({
                    url: routePaths.profile.children.course.children.discovery.path,
                    query: { topic_id: topicId },
                  })
                : querystring.stringifyUrl({ url: routePaths.course.index, query: { topic_id: topicId } })
            }
            prevPageTitle={'Khóa học'}
            categoryCourse={courseInfo.topic?.topicName}
            courseTitle={courseInfo.courseName ?? ''}
          />
        }
      />
      <div>
        <Description
          courseInfo={courseInfo}
          // className={'xl:pl-[84px]'}
          showMoreContent
          fillSvg={'#FFC41D'}
        />
      </div>
      <div className={'min-[1200px] m-auto max-w-7xl px-8 pt-6'}>
        {courseTypeId === CourseType.Standard ? (
          <div className="flex flex-col gap-4 pb-12 min-[1200px]:max-w-[620px]">
            <StandardCourseContent courseInfo={courseInfo} />
            {courseTypeId === CourseType.Standard && (
              <div className={'flex w-full justify-center lg:hidden'}>
                <div className={'relative m-auto w-full max-w-[320px] rounded-lg border border-[#f0f0f0]'}>
                  <Image
                    alt={courseInfo.courseName}
                    className={'w-full overflow-hidden rounded-t-lg'}
                    src={courseInfo.courseThumbnailImage ?? ''}
                    width={320}
                    height={160}
                  />
                  <div className={'p-4'}>
                    <LearnCourse courseInfo={courseInfo} />
                  </div>
                </div>
              </div>
            )}
            <Rating courseInfo={courseInfo} />
            {courseInfo?.courseFaq?.length > 0 && <Faq faqs={courseInfo?.courseFaq} />}
          </div>
        ) : (
          <div className={'mb-6'}>
            <ShortCourse fileUrl={courseInfo?.sections?.[0].lectures[0].video?.fileUrl || ''} />
          </div>
        )}
        <CourseOtherList author={author} />
        <CourseRelatedList relatedCourses={relatedCourses} />
      </div>
      <ViewMore
        title={`Bạn muốn tìm hiểu về Studify? <br /> Đăng ký ngay`}
        note={`Đăng ký tài khoản để tham gia cộng đồng học tập <br /> và trải nghiệm các tính năng tương tác mới lạ với bài
            học ngay trên Studify`}
        textBtn={'Đăng ký Studify'}
      />
      <Footer />
    </>
  );
};

export default CourseDetail;
