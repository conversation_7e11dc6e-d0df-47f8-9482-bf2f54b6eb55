import { SlideItemRequest, UploadedFile, useCourseCreationMutation } from '@/features/courses';
import { slideInitial } from '@/features/courses/creation/constants/slide';
import { CourseInfo, Section, SlideItem } from '@/features/courses/types';
import { LectureDetail } from '@/features/courses/types/lecture.type';
import { revalidatePath } from 'actions/revalidatePath';
import { Button, Modal, message, notification } from 'antd';
import Space from 'antd/es/space';
import CaseStudyContent from 'components/courses/CoureContent/SectionEditLayout/Content/caseStudyContent/CaseStudyContent';
import ExploreContent from 'components/courses/CoureContent/SectionEditLayout/Content/exploreContent/ExploreContent';
import InteractionContent from 'components/courses/CoureContent/SectionEditLayout/Content/interactionContent/InteractionContent';
import { ShortVideo } from 'components/courses/CoureContent/SectionEditLayout/Content/shortVideo';
import ControlCenter from 'components/courses/CoureContent/SectionEditLayout/Content/slideComponent/ControlCenter';
import SlideSegment from 'components/courses/CoureContent/SectionEditLayout/Content/slideComponent/SlideSegment';
import TitleLecture from 'components/courses/CoureContent/SectionEditLayout/Content/slideComponent/TitleLecture';
import { routePaths } from 'config';
import { MAX_SLIDE_AMOUNT } from 'constants/config';
import { AppMode } from 'constants/enum';
import { InteractionType } from 'constants/index';
import { isNil } from 'lodash-es';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { useContext, useEffect, useState, useTransition } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setCurrentSlideByFieldName,
  setSelectedSlideItem,
  setSlides,
  updateSlides,
} from 'reducer/courseReducer/slideSlice';
import { RootState } from 'store/store';
import UserInfoContext from 'utils/providers/UserInfoProvider';
import CollectionSlides from '../collectionSlides/CollectionSlides';
import '../index.scss';
import './index.scss';

type SlideContentProps = {
  courseInfo: CourseInfo | undefined;
  section: Section | undefined;
  lectureDetail: LectureDetail;
  fileAudio: UploadedFile | null | undefined;
  audios: UploadedFile[];
  images: UploadedFile[];
  onSetFileAudio: (value: null) => void;
};

const SlideContent = (props: SlideContentProps) => {
  const { courseInfo, fileAudio, lectureDetail, section, audios, onSetFileAudio } = props;
  const dispatch = useDispatch();
  const { userInfo: data } = useContext(UserInfoContext);
  const isShowPreviewBtn = data?.info?.id === courseInfo?.createdBy.id;
  const searchUrl = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const { isAddingSlide, onAddSlideItem, onDeleteSlide } = useCourseCreationMutation();

  const pathname = usePathname();

  const [numberOfMark, setNumberOfMark] = useState(7);

  const [isShowDeleteSlide, setIsShowDeleteSlide] = useState(false);

  const selectedSlideItem = useSelector((state: RootState) => state.slides.selectedSlideItem);
  const slides = useSelector((state: RootState) => state.slides.slides) || [];
  const selectedLecture = useSelector((state: RootState) => state.course.selectedLecture);

  useEffect(() => {
    if (selectedLecture && selectedLecture?.slide) {
      dispatch(setSlides(selectedLecture.slide.slideItems));
    }
  }, [selectedLecture, section]);

  useEffect(() => {
    if (lectureDetail.slide?.slideItems) {
      const slideItems = lectureDetail.slide.slideItems;
      if (!selectedSlideItem?.id && !slideItems?.find((slide) => slide?.id === selectedSlideItem?.id)) {
        dispatch(setSelectedSlideItem(slideItems[0]));
      }
    }
  }, [lectureDetail.slide?.slideItems, selectedSlideItem?.id]);

  useEffect(() => {
    if (!isNil(fileAudio)) {
      if (selectedSlideItem.slide_item_type_id === InteractionType.ShortVideo) {
        message.warning('Không thể thêm âm thanh cho clip ngắn');
      } else {
        handleUpdateFileAudioForSlide(fileAudio?.id);
        onSetFileAudio(null);
      }
    }
  }, [fileAudio]);

  const handleUpdateFileAudioForSlide = (fileId: string) => {
    const param = {
      properties: ['file_id'],
      value: fileId,
    };
    dispatch(setCurrentSlideByFieldName(param));
    dispatch(updateSlides(param));
  };

  const handleAddSlide = () => {
    if (isAddingSlide || isPending) return;
    const amountOfSlide = slides?.length || 0;

    if (!selectedLecture?.slide && amountOfSlide === undefined && !slides) {
      notification.error({
        message: ' Vui lòng thêm slide cho bài giảng trước! ',
      });
      return;
    }

    if (amountOfSlide >= MAX_SLIDE_AMOUNT) {
      notification.warning({
        message: 'Chỉ tạo tối đa 20 slide',
      });
      return;
    }

    const nextSlideNumber = amountOfSlide + 1;

    const getNextSortIndex = () => {
      if (amountOfSlide === 0) {
        return 1;
      }

      const maxSortIndex = Math.max(...slides.map((slide) => slide.sort_index || 0));
      return maxSortIndex + 1;
    };

    const sortIndex = getNextSortIndex();

    const getSlideItemName = () => {
      const baseName = `Slide ${nextSlideNumber}`;
      const isNameExists = slides?.some((slide) => slide.slide_item_name === baseName);

      if (isNameExists) {
        let counter = nextSlideNumber + 1;
        while (slides?.some((slide) => slide.slide_item_name === `Slide ${counter}`)) {
          counter++;
        }
        return `Slide ${counter}`;
      }

      return baseName;
    };

    const slideItemName = getSlideItemName();

    const newSlidePayload = {
      ...slideInitial,
      slide_item_type_id: InteractionType.Default,
      slide_item_name: slideItemName,
      sort_index: sortIndex,
    } satisfies SlideItemRequest;

    const existingSlides = slides.map((slide) => ({
      ...slide,
      sound: slide.sound?.id || null,
    }));

    const slidePayload = [...existingSlides, newSlidePayload] as SlideItemRequest[];

    onAddSlideItem(
      {
        courseId: courseInfo?.id?.toString() || '',
        sectionId: section?.id?.toString() || '',
        lectureId: selectedLecture?.id?.toString() || '',
        slideId: selectedLecture?.slide?.id?.toString() || '',
        payload: { data: slidePayload },
      },
      {
        onSuccess: async () => {
          startTransition(async () => {
            await revalidatePath(pathname, 'page');
            notification.success({ message: 'Đã thêm slide' });
          });
        },
      },
    );
  };

  const chooseSlideAfterDelete = (newSlides: SlideItem[]) => {
    if (newSlides.length && newSlides[newSlides.length - 1]) {
      const newSlide = newSlides[newSlides.length - 1];
      dispatch(setSelectedSlideItem(newSlide));
    }
  };

  const handleDeleteSlide = () => {
    if (!selectedSlideItem.id) {
      notification.warning({
        message: 'Vui lòng chọn slide để xóa',
      });
      return;
    }

    const newSlides = slides
      .filter((slide) => slide.id !== selectedSlideItem.id)
      .toSorted((a, b) => a.sort_index - b.sort_index);

    onDeleteSlide(
      {
        lectureId: lectureDetail.id,
        slideItemId: selectedSlideItem?.id || '',
        slideId: lectureDetail.slide?.id || '',
      },
      {
        onSuccess: async () => {
          await revalidatePath(pathname, 'page');
          dispatch(setSlides(newSlides));
          notification.success({ message: 'Xóa slide thành công' });
          chooseSlideAfterDelete(newSlides);
          setIsShowDeleteSlide(false);
        },
      },
    );
  };

  const getMainContent = () => {
    const { slide_item_type_id } = selectedSlideItem ?? { slide_item_type_id: 0 };
    switch (slide_item_type_id) {
      case InteractionType.Default:
        return <CollectionSlides type={selectedSlideItem.layout_type_id as number} />;
      case InteractionType.Question:
        return <InteractionContent />;
      case InteractionType.Explore:
        return <ExploreContent />;
      case InteractionType.Situation:
        return <CaseStudyContent />;
      case InteractionType.ShortVideo:
        return <ShortVideo urlFile={selectedSlideItem.short_clip?.file_url as string} />;
    }
  };

  const getNumberOfTotalSlides = () => {
    const sortedList = slides.toSorted((a, b) => a.sort_index - b.sort_index);
    const index = sortedList?.findIndex((slideItem) => slideItem.id === selectedSlideItem?.id) + 1 || 0;
    return `${index} / ${slides?.length || 0}`;
  };

  return (
    <>
      <Modal
        title={'Xoá Slide'}
        open={isShowDeleteSlide}
        onCancel={() => {
          setIsShowDeleteSlide(false);
        }}
        footer={[
          <Button key={'cancel'} className={'w-[110px]'} onClick={() => setIsShowDeleteSlide(false)}>
            Huỷ
          </Button>,
          <Button
            key={'delete'}
            loading={isAddingSlide}
            onClick={handleDeleteSlide}
            type={'primary'}
            className={'w-[110px] !bg-[#e44c10]'}
          >
            Xoá
          </Button>,
        ]}
      >
        <p>Bạn có chắc chắn muốn xóa slide này không? Bạn sẽ không thể phục hồi lại slide này sau khi đã xóa</p>
      </Modal>
      <div className={'slide-content h-full w-full'}>
        <div className={'px-[12px] py-[8px]'}>
          <div className={'center-y justify-between'}>
            <TitleLecture courseInfo={courseInfo} />
            {isShowPreviewBtn && (
              <Link
                href={queryString.stringifyUrl({
                  url: routePaths.learner.children.preview.path.replace(':courseId', courseInfo?.id.toString() || ''),
                  query: {
                    lectureId: searchUrl.get('lectureId'),
                    sectionId: section?.id,
                    mode: AppMode.Preview,
                  },
                })}
              >
                <Button type={'primary'} size={'small'}>
                  Xem trước bài học
                </Button>
              </Link>
            )}
          </div>

          <Space className={'relative w-full'} direction={'vertical'} size={12}>
            <div className={'slide mt-[8px] bg-[#ffffff]'} style={{ background: selectedSlideItem?.background_color }}>
              {getMainContent()}
            </div>

            <ControlCenter
              onSetSegment={handleAddSlide}
              onDeleteSegment={() => {
                if (selectedSlideItem?.id) {
                  setIsShowDeleteSlide(true);
                }
              }}
              numberOfMark={numberOfMark}
              onSetNumberOfMark={setNumberOfMark}
              numberOfSlides={getNumberOfTotalSlides()}
            />
            <SlideSegment audios={audios} numberOfMark={numberOfMark} />
          </Space>
        </div>
      </div>
    </>
  );
};

export default SlideContent;
