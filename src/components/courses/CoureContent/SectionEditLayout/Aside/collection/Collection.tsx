import { FileActions, FileType, formatFiles } from '@/constants/file';
import { UploadedFile, mapFileTypeToFileTypeId } from '@/features/courses';
import { LabelValueType } from '@/type';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons/lib/icons';
import { Col, Row, Tabs, message } from 'antd';
import Search from 'antd/es/input/Search';
import EmptyFile from 'components/courses/CoureContent/SectionEditLayout/Aside/collection/EmptyFile';
import ManagerFiles from 'components/courses/CoureContent/SectionEditLayout/Aside/managerFiles/ManagerFiles';
import { MAX_SIZE_VIDEO_UPLOAD } from 'constants/config';
import React from 'react';
import { useDispatch } from 'react-redux';
import { setFileType } from 'reducer/courseReducer/commonSlide';
import { maxFileSize } from 'utils';

type CollectionProps = {
  isShowSearch: boolean;
  onSetIsShowSearch: (value: boolean) => void;
  onSetSearchValue: (value: string) => void;
  onHandleSearchFile: (value: string) => void;
  handleUploadFile: (file: File) => void;
  handleUpdateFileName: (value: string, data: UploadedFile) => void;
  handleActionWithFile: (value: FileActions, file: UploadedFile) => void;
  onValidateBeforeUploadFile: (file: File) => boolean;
  searchValue: string;
  fileType: FileType;
  fileTypes: LabelValueType<FileType>[];
  dataSearch: any[];
  videos: { list: UploadedFile[]; total: number } | undefined;
  images: { list: UploadedFile[]; total: number } | undefined;
  audios: { list: UploadedFile[]; total: number } | undefined;
};
const Collection = ({
  isShowSearch,
  onSetIsShowSearch,
  searchValue,
  fileType,
  fileTypes,
  onSetSearchValue,
  onHandleSearchFile,
  handleUploadFile,
  handleUpdateFileName,
  handleActionWithFile,
  dataSearch,
  videos,
  images,
  audios,
  onValidateBeforeUploadFile,
}: CollectionProps) => {
  const dispatch = useDispatch();

  const validateMaxSize = (file: File) => {
    const messageMaxSize = maxFileSize(file.size, 'byte', MAX_SIZE_VIDEO_UPLOAD);
    if (messageMaxSize) {
      message.warning(messageMaxSize);
      return false;
    }
    return true;
  };
  const onChangeInputFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    if (validateMaxSize(files[0])) {
      handleUploadFile(files[0]);
    }

    event.target.value = '';
  };

  const fileAction = {
    [FileType.AUDIO]: audios,
    [FileType.VIDEO]: videos,
    [FileType.IMAGE]: images,
  } as Record<FileType, { list: UploadedFile[]; total: number } | undefined>;

  const handleGetFiles = () => {
    const fileTypeParams = fileType || FileType.VIDEO;
    let files = fileAction?.[fileTypeParams];
    if (searchValue) {
      files = { list: dataSearch, total: dataSearch.length };
    }
    if (!files?.list || files.list.length <= 0)
      return (
        <EmptyFile
          searchValue={searchValue}
          handleUploadFile={handleUploadFile}
          onValidateBeforeUploadFile={onValidateBeforeUploadFile}
        />
      );
    return (
      <ManagerFiles
        files={files?.list as UploadedFile[]}
        fileType={fileType}
        handleActionWithFile={handleActionWithFile}
        handleUpdateFileName={handleUpdateFileName}
      />
    );
  };

  return (
    <div className={'relative h-full w-[288px]'}>
      <div className={`${isShowSearch && '!py-0'} 'h-[50px] border-b-1 border-[#e5e5e9]' px-[16px] py-[14px]`}>
        {!isShowSearch && (
          <Row gutter={22} align={'middle'}>
            <Col span={18}>
              <p className={'font-semibold'}>Thư viện</p>
            </Col>
            <Col span={3}>
              <div className="image-upload">
                <label htmlFor="file-input">
                  <PlusOutlined className={'cursor-pointer'} />
                </label>
                <input
                  id="file-input"
                  type="file"
                  accept={
                    formatFiles.find(
                      (format: { value: string; file_type_id: number; type: FileType }) => fileType === format.type,
                    )?.value
                  }
                  onChange={onChangeInputFile}
                />
              </div>
            </Col>
            <Col span={3}>
              <SearchOutlined onClick={() => onSetIsShowSearch(true)} />
            </Col>
          </Row>
        )}

        {isShowSearch && (
          <Search
            placeholder="Nhập tên file"
            defaultValue={searchValue}
            allowClear
            onSearch={onHandleSearchFile}
            style={{ width: 244 }}
            size="small"
          />
        )}
      </div>

      <div className={'border-b-1 h-[36px] border-[#e5e5e9] px-[16px]'}>
        <div className={'flex items-center justify-between'}>
          <Tabs
            defaultActiveKey={FileType.VIDEO}
            activeKey={fileType}
            items={fileTypes.map((item) => {
              return {
                label: <span>{item.label}</span>,
                key: item.value,
              };
            })}
            onChange={(value) => {
              onSetSearchValue('');

              const fileTypeId = mapFileTypeToFileTypeId(value as FileType);
              if (value === FileType.SLIDE) {
                dispatch(setFileType(mapFileTypeToFileTypeId(FileType.AUDIO)));
              } else {
                dispatch(setFileType(fileTypeId));
              }
            }}
            centered
          />
        </div>
      </div>
      {handleGetFiles()}
    </div>
  );
};

export default Collection;
