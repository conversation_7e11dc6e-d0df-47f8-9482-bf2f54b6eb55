import { CourseDetailInfo, Section, swapLecture, swapSection } from '@/features/courses';
import { swapElements } from '@/utils/array.util';
import { getTargetAndResultIdx } from 'components/courses/CoureContent/components/ChapterList/getTargetAndResult';
import { isInteger } from 'lodash-es';
import { useEffect, useState } from 'react';
import { useMutation } from 'react-query';

const checkIsValidMove = (course: CourseDetailInfo, destinationIndex: number) => {
  const { targetIdx, testIdx, resultIdx } = getTargetAndResultIdx(course);

  if (
    (destinationIndex <= targetIdx && isInteger(targetIdx)) ||
    (destinationIndex >= testIdx && isInteger(testIdx)) ||
    (destinationIndex >= resultIdx && isInteger(resultIdx))
  ) {
    return false;
  }

  return true;
};

export function useCourseState(courseDefault: CourseDetailInfo | undefined) {
  const [course, setCourse] = useState<CourseDetailInfo>();

  const { mutate: onSwapLecture } = useMutation({
    mutationFn: swapLecture,
  });

  const { mutate: onSwapSection } = useMutation({
    mutationFn: swapSection,
  });

  const sortedSections = course?.sections?.sort((a, b) => a.sortIndex - b.sortIndex) ?? [];

  const handleSwapLecture = (sourceIndex: number, destinationIndex: number, section: Section, sectionIdx: number) => {
    if (sourceIndex === destinationIndex || !course) return;

    const lectures = section.lectures || [];
    const clonedCourse = structuredClone(course);

    const isValidMove = checkIsValidMove(clonedCourse, destinationIndex);
    if (!isValidMove) return;

    const sourceLecture = lectures[sourceIndex];
    const destinationLecture = lectures[destinationIndex];

    if (!sourceLecture || !destinationLecture) return;

    const updatedLectures = swapElements(lectures, sourceIndex, destinationIndex);
    const updatedSections =
      clonedCourse.sections?.map((section, index) => {
        if (index === sectionIdx) {
          return { ...section, lectures: updatedLectures };
        }
        return section;
      }) || [];

    onSwapLecture({ courseId: course.id, sectionId: section.id, from: sourceLecture.id, to: destinationLecture.id });
    setCourse({ ...clonedCourse, sections: updatedSections });
  };

  const handleSwapSection = (sourceIndex: number, destinationIndex: number) => {
    if (sourceIndex === destinationIndex || !course) return;

    const sections = course.sections || [];
    const clonedCourse = structuredClone(course);

    const isValidMove = checkIsValidMove(clonedCourse, destinationIndex);
    if (!isValidMove) return;

    const sourceSection = sections[sourceIndex];
    const destinationSection = sections[destinationIndex];

    if (!sourceSection || !destinationSection) return;

    const updatedSections = swapElements(sections, sourceIndex, destinationIndex);

    onSwapSection({ courseId: course.id, from: sourceSection.id, to: destinationSection.id });
    setCourse({ ...clonedCourse, sections: updatedSections });
  };

  useEffect(() => {
    if (!courseDefault) return;
    setCourse(courseDefault);
  }, [courseDefault]);

  return {
    course,
    sortedSections,
    setCourse,
    handleSwapLecture,
    // handleSwapChapter,
    handleSwapSection,
  };
}
