import { revalidatePath } from '@/actions/revalidatePath';
import { Section, useCourseCreationMutation } from '@/features/courses';
import useCreateCourseApi from '@/features/courses/creation/hooks/useCreateCourseApi';
import { App, Skeleton } from 'antd';
import AddSection from 'components/add-section';
import SectionAction from 'components/section-action';
import SectionDragDrop from 'components/section-drag-drop';
import SectionDraggableItem from 'components/section-draggable-item';
import SectionEmpty from 'components/section-empty';
import SectionTitle from 'components/section-title';
import { ActionSectionValue } from 'config/constant';
import { Display } from 'constants/enum';
import { isNil } from 'lodash-es';
import { useRouter } from 'next-nprogress-bar';
import dynamic from 'next/dynamic';
import { usePathname, useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { Fragment, Suspense, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setIsShowModalDesignContent } from 'reducer/courseReducer/courseSlice';
import { ChapterListProps } from './ChapterListProps';
import './index.scss';
import { useCourseState } from './useCourseState';
import { useEditContent } from './useEditContent';
import { useLecture } from './useLecture';

const SectionDeleteModal = dynamic(() => import('components/section-delete-modal'), {
  ssr: false,
});

const ModalUploadThumb = dynamic(() => import('components/courses/CoureContent/components/ModalUploadThumb'), {
  ssr: false,
});
const ModalEditTitleName = dynamic(() => import('components/courses/CoureContent/components/ModalEditChapter'), {
  ssr: false,
});
const ModalDeleteLecture = dynamic(() => import('components/courses/CoureContent/components/ModalDeleteLecture'), {
  ssr: false,
});

const LectureDraggable = dynamic(() => import('components/lecture-draggable'), {
  ssr: false,
  loading: () => (
    <div className={'flex w-full flex-col justify-center gap-2 text-center'}>
      <Skeleton.Button active style={{ height: 84, width: 814 }} />
      <Skeleton.Button active style={{ height: 84, width: 814 }} />
      <Skeleton.Button active style={{ height: 84, width: 814 }} />
    </div>
  ),
});

const ChapterList = (props: ChapterListProps) => {
  const { onSetDisplay, setIsShowChapter, courseInfo } = props;
  const searchParams = useSearchParams();
  const router = useRouter();
  const dispatch = useDispatch();

  const { notification } = App.useApp();
  const { editLectureName, updateLecture } = useCreateCourseApi();

  const {
    isOpenEditLecture,
    setIsOpenEditLecture,
    setIsOpenDeleteLecture,
    isOpenUploadLectureThumb,
    onSetUpdateLecture,
    selectedLecture,
    setIsOpenUploadLectureThumb,
    isOpenDeleteLecture,
    onDeleteLecture,
    handleUpdateThumbImage,
    handleOpenDeleteLectureModal,
    handleOpenEditLectureModal,
  } = useLecture();
  const [isOpenEditChapter, setIsOpenEditChapter] = useState(false);
  const [isOpenDeleteChapter, setIsOpenDeleteChapter] = useState(false);

  const [selectedChapter, setSelectedChapter] = useState<Section>();

  const { openEditContent, handleOpenChange, hideEditContent } = useEditContent();

  const { sortedSections, handleSwapLecture, handleSwapSection } = useCourseState(courseInfo);

  const { sectionDeleting, onEditSection, onDeleteSection } = useCourseCreationMutation();

  const pathname = usePathname();

  const handleUpdateThumbnail = async (fileId: string) => {
    if (isOpenUploadLectureThumb && selectedLecture) {
      try {
        await updateLecture({
          courseId: courseInfo?.id?.toString() || '',
          sectionId: selectedChapter?.id || '',
          lectureId: selectedLecture?.id || '',
          payload: { fileId },
        });
        await revalidatePath(pathname, 'page');
        notification.success({
          message: 'Cập nhật ảnh đại diện bài học thành công',
        });
        setIsOpenUploadLectureThumb(false);
      } catch (error) {
        console.error('Error updating lecture thumbnail:', error);
      }
    }
  };

  const handleEditLectureName = async (formValues: { lecture?: string }) => {
    try {
      await editLectureName({
        courseId: courseInfo?.id?.toString() || '',
        sectionId: selectedChapter?.id || '',
        lectureId: selectedLecture?.id || '',
        payload: { lectureName: formValues.lecture || '' },
      });
      await revalidatePath(pathname, 'page');
      notification.success({
        message: 'Cập nhật tên bài học thành công',
      });
      setIsOpenEditLecture(false);
    } catch (error) {
      console.error('Error editing lecture name:', error);
    }
  };

  const onRedirectPage = (chapter: Section) => {
    dispatch(setIsShowModalDesignContent(true));
    router.replace(
      queryString.stringifyUrl({
        url: pathname,
        query: {
          sectionId: chapter.id,
        },
      }),
      {
        scroll: false,
      },
    );
  };

  const onSetChapterTitle = (data: { chapter?: string; lecture?: string }) => {
    if (!isOpenEditChapter || !selectedChapter) return;
    onEditSection({
      courseId: courseInfo?.id?.toString() || '',
      sectionId: selectedChapter?.id?.toString() || '',
      sectionName: data.chapter || '',
    });
  };

  const onDeleteChapter = () => {
    if (isOpenDeleteChapter && selectedChapter) {
      onDeleteSection(
        { courseId: courseInfo?.id?.toString() || '', sectionId: selectedChapter?.id?.toString() || '' },
        {
          onSuccess: () => {
            setIsOpenDeleteChapter(false);
          },
        },
      );
    }
  };

  const onActionChapter = (value: ActionSectionValue, section: Section) => {
    switch (value) {
      case ActionSectionValue.EditSectionContent:
        router.push(
          queryString.stringifyUrl({
            url: pathname,
            query: {
              ...queryString.parse(searchParams.toString()),
              sectionId: section.id,
              edit: true,
              lectureId: section?.lectures[0]?.id,
              slideId: section?.lectures[0]?.slideId,
            },
          }),
        );
        break;
      case ActionSectionValue.EditSectionName:
        setIsOpenEditChapter(true);
        break;
      case ActionSectionValue.DeleteSection:
        setIsOpenDeleteChapter(true);
        break;
    }
  };

  const handleSectionAction = (section: Section, action: ActionSectionValue) => {
    onActionChapter(action, section);
    setSelectedChapter(section);
    hideEditContent();
  };

  const handleShowAddSection = () => {
    onSetDisplay(Display.AddChapter);
    setIsShowChapter(false);
  };

  return (
    <>
      <SectionDragDrop
        droppableChildren={
          <div className={'smb-[12px] flex w-full flex-col gap-2'}>
            {sortedSections.map((section, index) => (
              <Fragment key={`course-section-${section.id}`}>
                <SectionDraggableItem
                  onDragEndLecture={(result) => {
                    const desIndex = result?.destination?.index;
                    if (!isNil(desIndex)) {
                      handleSwapLecture(result.source.index, desIndex, section, index);
                    }
                  }}
                  createNewSection={<SectionEmpty onCreateSection={() => onRedirectPage(section)} />}
                  sectionTitle={
                    <SectionTitle
                      popoverProps={{
                        open: openEditContent.includes(section.id),
                        content: <SectionAction handleGetAction={(action) => handleSectionAction(section, action)} />,
                        onOpenChange: (open) => handleOpenChange(open, section.id),
                      }}
                      section={section}
                    />
                  }
                  section={section}
                  index={index}
                  lectureDraggable={
                    <LectureDraggable
                      handleUpdateThumbImage={(lecture) => {
                        // TODO: reopen this after BE team implements API upload lecture thumbnail
                        // handleUpdateThumbImage(lecture);
                        // setSelectedChapter(section);
                      }}
                      onCreateLecture={onRedirectPage}
                      onAddSection={handleShowAddSection}
                      section={section}
                      onOpenDeleteLectureModal={handleOpenDeleteLectureModal}
                      onOpenEditLectureModal={(lecture) => {
                        handleOpenEditLectureModal(lecture);
                        setSelectedChapter(section);
                      }}
                    />
                  }
                />
              </Fragment>
            ))}
          </div>
        }
        onDragEnd={(result) => {
          const srcIndex = result.source.index;
          const desIndex = result?.destination?.index;
          if (!isNil(desIndex)) {
            handleSwapSection(srcIndex, desIndex);
          }
        }}
      />
      <AddSection
        onAddSection={() => {
          onSetDisplay(Display.AddChapter);
          setIsShowChapter(false);
        }}
      />
      <Suspense>
        <ModalEditTitleName
          type={'chapter'}
          open={isOpenEditChapter}
          onShow={setIsOpenEditChapter}
          onSetTitle={onSetChapterTitle}
          selectedItem={selectedChapter}
        />
        <SectionDeleteModal
          modalProps={{
            open: isOpenDeleteChapter,
            onCancel: () => setIsOpenDeleteChapter(false),
          }}
          submitProps={{
            onClick: onDeleteChapter,
            loading: sectionDeleting,
          }}
        />
        <ModalEditTitleName
          type={'lecture'}
          open={isOpenEditLecture}
          onShow={setIsOpenEditLecture}
          onSetTitle={handleEditLectureName}
          selectedItem={selectedLecture}
        />
        <ModalDeleteLecture
          isOpenDeleteLecture={isOpenDeleteLecture}
          setIsOpenDeleteLecture={setIsOpenDeleteLecture}
          onDeleteLecture={onDeleteLecture}
        />
        <ModalUploadThumb
          open={isOpenUploadLectureThumb}
          onShow={setIsOpenUploadLectureThumb}
          onSetUpdateLecture={onSetUpdateLecture}
          onUploadImage={handleUpdateThumbnail}
        />
      </Suspense>
    </>
  );
};

export default ChapterList;
