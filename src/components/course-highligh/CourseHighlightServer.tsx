import { getPublicCoursesByFilter, getPublicTopics } from '@/features/courses/services/server';
import CourseHighlight from 'components/course-highligh/CourseHighlight';
import { AppProps } from 'type/appProps';

export default async function CourseHighlightServer({ searchParams }: Readonly<AppProps>) {
  const publicTopics = await getPublicTopics();

  const publicCourseDetail = await getPublicCoursesByFilter({
    limit: 8,
    topic: searchParams.topic_id?.toString() ?? '',
  });

  return <CourseHighlight courses={publicCourseDetail.data} topics={publicTopics.data} />;
}
