'use client';
import { CourseInfo, Topic, useFavoriteCourse } from '@/features/courses';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { routePaths } from 'config';
import { CourseType } from 'constants/enum';
import { useQuerySearch } from 'hooks';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import queryString from 'query-string';
import { useContext, useMemo } from 'react';
import 'swiper/css';
import { Swiper, SwiperSlide } from 'swiper/react';
import UserInfoContext from 'utils/providers/UserInfoProvider';

const HomeCourseItem = dynamic(() => import('components/home/<USER>'));
const categoryHighlightList = [
  {
    value: '0',
    label: 'Tất cả chủ đề',
  },
];

export type CourseHighlightProps = {
  courses: CourseInfo[];
  topics: Topic[];
};

function CourseHighlight(props: Readonly<CourseHighlightProps>) {
  const { topics, courses } = props;
  const { topic_id } = useQuerySearch<{ topic_id?: string }>();

  const { isLoggedIn } = useContext(UserInfoContext);

  const { onFavorite } = useFavoriteCourse();

  const categoryHighlights = useMemo(() => {
    const items = [...categoryHighlightList];
    topics.forEach((topic, index) => {
      if (index < 5) {
        items.push({ value: topic.id.toString(), label: topic.topicName });
      }
    });
    return items;
  }, [topics]);

  const CourseItem = ({ item }: { item: CourseInfo }) => (
    <HomeCourseItem
      isShortCourse={item.courseTypeId === CourseType.Short}
      totalRate={Number(item.totalRating ?? 0)}
      category={item.topic?.topicName ?? ''}
      // slug={item.slug}
      avg_rating={Number(item.avgRating) ?? 5}
      course_description={item.courseDescription}
      duration={Number(item.courseDuration) ?? 0}
      totalLearner={item.totalLearner ?? 0}
      course_thumbnail_image={item.courseThumbnailImage ?? ''}
      avgRate={Number(item.avgRating) ?? 5}
      course_name={item.courseName}
      sections_count={item.totalSections ?? 0}
      id={item.id}
      isMajor
      isAuthor
      authorName={item.createdBy?.name}
      course_level_id={Number(item.courseLevelId)}
      onLike={(isLike: boolean) => onFavorite({ courseId: item.id, isFavorite: isLike })}
      isFavorite={Boolean(item.isFavorite)}
      isLoggedIn={isLoggedIn}
      topicName={item.topic?.topicName}
      isLiking={Boolean(item.isFavorite)}
    />
  );

  return (
    <div className={'bg-neutral-50'}>
      <div className={'xl:px-0 m-auto max-w-7xl p-2 pb-12 lg:pb-24'}>
        <div className={'pb-4 pt-8 text-2xl font-semibold md:pt-20 md:text-center md:text-3xl'}>Khóa học nổi bật</div>
        <div className={'pb-12 text-xl md:px-36 md:text-center md:text-xl'}>
          Hãy bắt đầu hành trình khám phá và học tập của bạn bằng việc tham gia vào Khóa học nổi bật đến từ các chuyên
          gia và nhà sáng tạo hàng đầu của Studify
        </div>
        <div className={'flex items-center justify-between'}>
          <Swiper className={'m-0 !ml-0'} slidesPerView={'auto'}>
            {categoryHighlights.map((item) => (
              <SwiperSlide key={item.value} className={'mr-3 w-auto min-w-[157px] cursor-pointer text-center'}>
                <Link
                  shallow={true}
                  scroll={false}
                  href={queryString.stringifyUrl({
                    url: routePaths.homePage,
                    query: {
                      topic_id: item.value.toString() === '0' ? undefined : item.value,
                    },
                  })}
                >
                  <div
                    className={`${
                      item.value === (topic_id ?? '0') ? 'bg-primary-100 text-primary' : ''
                    } rounded-4xl px-3 py-2 font-semibold text-black hover:bg-primary-100 hover:text-primary`}
                  >
                    {item.label}
                  </div>
                </Link>
              </SwiperSlide>
            ))}
          </Swiper>
          <Link
            href={routePaths.course.index}
            className={
              'ml-4 hidden min-w-[270px] cursor-pointer items-center gap-2 pl-10 font-semibold text-primary hover:underline lg:flex'
            }
          >
            <span>Khám phá các chủ đề khác</span>
            <div>
              <ArrowRightIcon className={'size-4 font-semibold hover:animate-pulse'} />
            </div>
          </Link>
        </div>
        <div className={'hidden md:block'}>
          <div className={'xl:grid-cols-4 grid gap-6 pt-4 md:grid-cols-2 lg:grid-cols-3'}>
            {courses?.map((item) => (
              <div key={item.id}>
                <CourseItem item={item as any} />
              </div>
            ))}
          </div>
        </div>
        <div className={'mt-6 md:hidden'}>
          <Swiper slidesPerView={'auto'}>
            {courses.map((item) => (
              <SwiperSlide className={'mr-6 h-full !w-[250px]'} key={item.id}>
                <CourseItem item={item as any} />
              </SwiperSlide>
            ))}
            <div className={'mt-6 flex justify-center'}>
              <button
                className={
                  'flex h-12 items-center justify-center rounded-sm bg-secondary-500 px-6 hover:bg-secondary-300'
                }
              >
                <strong>Xem thêm</strong>
              </button>
            </div>
          </Swiper>
        </div>
      </div>
    </div>
  );
}

export default CourseHighlight;
