import { CourseInfo, LectureDetail, Section, SlideItem } from '@/features/courses/types/';
import { DD_MM_YYYY, HH_MM, HYPERNOTE_TIME_STEP } from 'components/learner/constant';
import { ChapterStatus, HyperNoteBody } from 'components/learner/type';
import { LectureType, SectionType } from 'config';
import dayjs from 'dayjs';
import { xor } from 'lodash-es';
import { UserAnswer } from 'type';

export const returnStatusChapter = (preSection: Section, section: Section, courseInfo: CourseInfo) => {
  const { completedLectures = [] } = courseInfo;

  const completedLectureIds = completedLectures?.map((lecture) => lecture.lecture_id);
  const isCompletedAllLectureInPreSection = preSection?.lectures.every((lecture) =>
    completedLectureIds.includes(lecture.id),
  );
  if (completedLectures?.length === 0) {
    // check lock/ unlock section
    return section.publish === 1 ? ChapterStatus.Locked : ChapterStatus.Uncheck;
  }

  const completedLecturesFiltered = section.lectures.filter((lecture) => completedLectureIds.includes(lecture.id));
  if (completedLecturesFiltered.length === 0 || section.lectures.length === 0) {
    return section.publish === 1 && !isCompletedAllLectureInPreSection ? ChapterStatus.Locked : ChapterStatus.Uncheck;
  }
  if (completedLecturesFiltered.length === section.lectures.length) {
    return ChapterStatus.Checked;
  }
  return ChapterStatus.Studying;
};

export const checkIsLockedSection = (preSection: Section | undefined, courseInfo: CourseInfo) => {
  const { completedLectures = [] } = courseInfo;
  const completedLectureIds = completedLectures.map((lecture) => lecture.lecture_id);
  const isCompletedAllLectureInPreSection = preSection?.lectures.every((lecture) =>
    completedLectureIds.includes(lecture.id),
  );
  if (!preSection) return false;
  return courseInfo.isSequential === 1 && !isCompletedAllLectureInPreSection;
};

export const returnStatusLecture = (lecture: LectureDetail, courseInfo: CourseInfo, lectureId: string) => {
  const { completedLectures = [] } = courseInfo;
  const completedLectureIds = completedLectures.map((lecture) => lecture.lecture_id);
  const isCompletedLecture = completedLectureIds.includes(lecture.id);
  if (lecture.id === lectureId && !isCompletedLecture) {
    return ChapterStatus.Studying;
  }
  return isCompletedLecture ? ChapterStatus.Checked : ChapterStatus.Uncheck;
};

export const checkAnswerQuestion = (
  currentSlideIdx: number,
  currentSlide: SlideItem,
  selectedLecture: LectureDetail,
) => {
  if (!currentSlide.question?.question_required) return true;
  return selectedLecture?.user_answers?.find(
    (userAnswer: UserAnswer) => String(userAnswer.question_id) === (currentSlide.question?.id ?? ''),
  );
};

export const getColorForAnswer = (answer: number, correct_answer: number[], userAnswers: number[]) => {
  const unCorrectAnswer = {
    bgColor: '#E5E5E9',
    borderColor: '#E5E5E9',
    color: 'none',
  };
  const differenceAnswers = xor(correct_answer, userAnswers);
  if (differenceAnswers.length <= 0) {
    return correct_answer.includes(answer)
      ? {
          bgColor: '#EBEBFF',
          borderColor: '#1B1DFB',
          color: 'blue',
        }
      : unCorrectAnswer;
  }
  if (!correct_answer.includes(answer)) {
    return userAnswers.includes(answer)
      ? {
          bgColor: '#FACFCF',
          borderColor: '#EE1111',
          color: 'red',
        }
      : unCorrectAnswer;
  }

  return userAnswers.includes(answer)
    ? {
        bgColor: '#bbf7d0',
        borderColor: '#16a34a',
        color: 'green',
      }
    : {
        bgColor: '#EBEBFF',
        borderColor: '#1B1DFB',
        color: 'blue',
      };
};

export const handleGetHypersInSameSlide = (hypers: HyperNoteBody[]) => {
  let newHypers: { index: number; items: HyperNoteBody[] }[] = [];
  hypers.forEach((hyper) => {
    const idx = newHypers.findIndex(
      (newHyper: { index: number; items: HyperNoteBody[] }) => hyper.extra_info?.slide.slide_id === newHyper.index,
    );
    if (idx !== -1) {
      newHypers[idx] = { ...newHypers[idx], items: [...newHypers[idx].items, hyper] };
    } else newHypers.push({ index: hyper.extra_info?.slide.slide_id!, items: [hyper] });
  });
  return newHypers;
};

export const handleHyperData = (hyperData: HyperNoteBody[], step: number) => {
  // output trả về mảng các cụm hyper có tgian xuất hiện gần nhau
  const hyperDataSorted = hyperData.toSorted(
    (a: HyperNoteBody, b: HyperNoteBody) => a?.extra_info?.current_time! - b?.extra_info?.current_time!,
  );
  let mediatingArr: HyperNoteBody[] = [];
  const finalArr: HyperNoteBody[][] = [];

  hyperDataSorted.forEach((hyper: HyperNoteBody, index) => {
    const current_time = (hyper?.extra_info?.current_time as number) || 0;
    const firstTimeOfMediating = mediatingArr[0]?.extra_info?.current_time as number;

    if (current_time - firstTimeOfMediating <= step || index === 0) {
      // khi các hyper có time gần nhau
      mediatingArr.push(hyper);
      finalArr.push(mediatingArr);
      return;
    }
    mediatingArr = [hyper];
    finalArr.push([hyper]);
  });
  return finalArr;
};

export const getTimeHyper = (time: string) => {
  return {
    day: dayjs(time).format(DD_MM_YYYY),
    hour: dayjs(time).format(HH_MM),
  };
};

export const getNearestQuestions = (hypers: HyperNoteBody[], value: HyperNoteBody) => {
  const currentIdx = hypers.findIndex((hyper) => hyper.id === value?.id);
  if (currentIdx !== -1) {
    const nearestHypers: HyperNoteBody[] = [];
    hypers.forEach((hyper: HyperNoteBody) => {
      const currentQuestionTime = value?.extra_info?.current_time ?? 0;
      const validCase1 =
        currentQuestionTime - hyper.extra_info?.current_time! <= HYPERNOTE_TIME_STEP &&
        currentQuestionTime - (hyper.extra_info?.current_time || 0) >= 0;
      const validCase2 =
        (hyper.extra_info?.current_time || 0) - currentQuestionTime <= HYPERNOTE_TIME_STEP &&
        (hyper.extra_info?.current_time || 0) - currentQuestionTime >= 0;
      if (validCase1 || validCase2) {
        nearestHypers.push(hyper);
      }
    });
    return nearestHypers;
  }
  return [];
};

export const sectionName = {
  [SectionType.Test]: 'Bài kiểm tra',
  [SectionType.Target]: 'Mục tiêu học tập',
  [SectionType.Result]: 'Kết quả học tập',
  [SectionType.Default]: 'Bài giảng video',
};

export const getSectionName = (currentChapter: Section | null) => {
  if (!currentChapter) return 'Bài giảng video';
  const { sectionTypeId } = currentChapter;
  return sectionName[sectionTypeId];
};
export const lectureName = {
  [LectureType.Test]: 'Bài kiểm tra',
  [LectureType.Target]: 'Mục tiêu học tập',
  [LectureType.Result]: 'Kết quả học tập',
  [LectureType.Video]: 'Bài giảng video',
  [LectureType.Slide]: 'Bài giảng slide',
};

export const getLectureName = (lecture: LectureDetail | null) => {
  if (!lecture) return 'Bài giảng video';
  const { lectureTypeId } = lecture;
  return lectureName[lectureTypeId as keyof typeof lectureName];
};
