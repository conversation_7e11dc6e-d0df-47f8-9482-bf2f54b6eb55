'use client';

import { CourseInfo, Section } from '@/features/courses/types';
import ArrayBottomIcon from 'icons/ArrayBottomIcon';
import ChaptersIcon from 'icons/ChaptersIcon';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useCallback, useState } from 'react';
import TargetContainerTab from '../targetContainer/targetContainerTab';
import './resultContainer.scss';

const TargetContainerBottom = dynamic(() => import('../targetContainer/targetContainerBottom'), {
  ssr: false,
  loading: () => (
    <div className="fixed bottom-0 left-0 right-0 bg-black p-4 text-white">
      <div className="flex justify-between">
        <div>Kết quả học tập</div>
        <button className="mr-8 inline-flex cursor-pointer text-white">
          <ChaptersIcon />
        </button>
      </div>
    </div>
  ),
});

const TargetOverviewInfo = dynamic(() => import('../targetContainer/targetOverviewInfo'), {
  ssr: false,
});

const ReferDocuments = dynamic(() => import('../targetContainer/referDocuments'), {
  ssr: false,
});

const StudyMaterials = dynamic(() => import('../targetContainer/studyMaterials'), {
  ssr: false,
});

export const ResultContainer = ({
  learnerCourse,
  currentSection,
}: {
  learnerCourse: CourseInfo;
  currentSection: Section;
}) => {
  const { learningGoal } = currentSection;
  const [showMore, setShowMore] = useState(false);
  const { userCompletedLecture: completedLectures = [], totalLectures } = learnerCourse;
  const tempPercent = totalLectures ? +Math.round((completedLectures.length * 100) / totalLectures).toFixed(2) : 100;
  const [intervalVal, setIntervalVal] = useState<NodeJS.Timeout>();

  const getPreSection = useCallback(() => {
    if (!learnerCourse) return { sectionName: '' };
    let preSection = { sectionName: '' };
    const sections = learnerCourse.sections ?? [];
    sections.forEach((section: Section, index: number, originalSections: Section[]) => {
      if (section.id === currentSection.id) {
        preSection = originalSections[index - 1];
      }
    });
    return preSection;
  }, [learnerCourse, currentSection]);

  return (
    <div onClick={() => clearInterval(intervalVal)} role={'button'} tabIndex={0} className="result-container">
      <div className="bg-black px-2 py-4 text-white">&nbsp;</div>
      {!showMore ? (
        <div className="flex w-full flex-wrap items-center justify-center align-middle">
          <div>
            <Image
              quality={80}
              width={414}
              height={430}
              className={'inline-block max-h-[430px] max-w-[414px]'}
              src="/images/result_learner_left.jpg"
              alt={'result learner left'}
            />
          </div>
          <div>
            <h5 className="result-container__title">Chúc mừng!</h5>
            <p className="result-container__description">Bạn đã hoàn thành chương {getPreSection()?.sectionName}:</p>
            <strong className="text-3xl">{currentSection?.sectionName}</strong>
            <p className="result-container__description">
              Thời lượng đã hoàn thành khóa học: <strong>{tempPercent}%</strong>
            </p>
            <div className="mt-8">
              <button className="flex items-center align-middle hover:text-primary" onClick={() => setShowMore(true)}>
                <span className="wrapper-icon-arrow border hover:border-blue-500">
                  <ArrayBottomIcon />
                </span>
                <span>Bấm vào đây để tìm hiểu thêm</span>
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="container py-6">
          <strong className="my-5 text-xl">Kết quả học tập</strong>
          <div className={'grid w-full grid-cols-1 gap-4 md:grid-cols-2'}>
            <div>
              <TargetOverviewInfo learning_goals={learningGoal.learning_goal_content} />
            </div>
            <div>
              <ReferDocuments refer_documents={learningGoal.refer_documents} />
              <StudyMaterials study_materials={learningGoal?.study_materials} />
            </div>
          </div>
        </div>
      )}
      <TargetContainerTab
        setIntervalValue={setIntervalVal}
        courseInfo={learnerCourse}
        currentSection={currentSection}
        isTarget={false}
      />
      <TargetContainerBottom label="Kết quả học tập" />
    </div>
  );
};

export default ResultContainer;
