'use client';

import { processCourse } from '@/features/courses';
import { LectureDetail } from '@/features/courses/types';
import { getIndexSlideById, getLectureInCourse, getSlideInLecture } from '@/features/courses/utils';
import { Dropdown, Form, MenuProps } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { LearnerMainContent } from 'components/learner-main-content';
import { MAX_HYPERNOTE } from 'components/learner/constant';
import { HyperNoteBody, HyperNoteParams, LearnerInteractionType } from 'components/learner/type';
import { validPinnedHyper, validateLearner } from 'components/learner/utils';
import {
  notifyDeleteHyperNoteSuccess,
  notifyPinedHyperNoteSuccess,
  notifyUpdateHyperNoteSuccess,
} from 'components/learner/utils/notify';
import { LectureType, SectionType } from 'config/constant';
import { AppMode } from 'constants/enum';
import { InteractionType } from 'constants/index';
import { SOURCE_URL, defaultQuery } from 'constants/query';
import { useCourse } from 'hooks/apis/course';
import { useHyperNote } from 'hooks/apis/learner/hypernote';
import { useInteractionVideo } from 'hooks/learner/useInteractionVideo';
import { useNextLecture } from 'hooks/learner/useNextLecture';
import { useFullScreen } from 'hooks/use-fullscreen';
import { useDebounce } from 'hooks/useDebounce';
import { useQuerySearch } from 'hooks/useQuerySearch';
import { useVideoContext } from 'hooks/useVideoContext';
import AddNoteIcon from 'icons/AddNote';
import dynamic from 'next/dynamic';
import { Suspense, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { setIsInteractionWithHyper } from 'reducer/courseReducer/learnerSlice';
import { setSlides } from 'reducer/courseReducer/slideSlice';
import { RootState } from 'store/store';
import { LectureDocument, LectureSegment, UpdateSlides } from 'type';
import { getBodyCreateNote } from 'utils';
import UserInfoContext from 'utils/providers/UserInfoProvider';
import { LearnerContainerProps } from './LearnerContainerProps';
import { LearnerSectionName } from './LearnerSectionName';
import './index.scss';

const InteractionVideo = dynamic(() => import('components/interaction-video'));
const SlideContainer = dynamic(() => import('components/learner/components/slideContainer/SlideContainer'));
const TestContainer = dynamic(() => import('components/learner/components/testContainer'));
const NoteDrawer = dynamic(() => import('components/learner/components/noteDrawer/NoteDrawer'), {
  ssr: false,
});
const ControlVideo = dynamic(() => import('components/control-video'), {
  ssr: false,
});
const FilterNote = dynamic(() => import('components/learner/components/noteDrawer/filterNote/FilterNote'), {
  ssr: false,
});
const PauseModal = dynamic(() => import('components/learner/components/pauseModal/PauseModal'), {
  ssr: false,
});
const Notes = dynamic(() => import('components/learner/components/noteDrawer/notes/Notes'), {
  ssr: false,
});

const HyperNotePopup = dynamic(() => import('components/learner/components/hyperNotePopup/HyperNotePopup'), {
  ssr: false,
});

const CreateNoteModal = dynamic(() => import('components/learner/components/modals/CreateNoteModal'), {
  ssr: false,
});
const DeleteNoteModal = dynamic(() => import('components/learner/components/modals/DeleteNoteModal'), {
  ssr: false,
});
const UpdateNoteModal = dynamic(() => import('components/learner/components/modals/UpdateNoteModal'), {
  ssr: false,
});
const ViewNoteModal = dynamic(() => import('components/learner/components/modals/ViewNoteModal'), {
  ssr: false,
});
const QuestionPopup = dynamic(() => import('components/learner/components/questionPopup/QuestionPopup'), {
  ssr: false,
});

export const LearnerContainer = (props: LearnerContainerProps) => {
  const dispatch = useDispatch();
  const { selectedLecture, courseInfo, currentSection, isLearnerPreview, lectureTestData, setIsReviewCourse } = props;
  const searchQuery = useQuerySearch();
  const { lectureId, sectionId, slideId } = searchQuery;
  const [form] = useForm();

  const { createHyperNote, deleteHyperNote, updateHyperNote, updatePinnedHyperNote, getHyperNotes } = useHyperNote();
  const { updateSlideItems } = useCourse();

  const { userInfo } = useContext(UserInfoContext);
  const isPreviewMode = userInfo?.info?.id === courseInfo.createdBy?.id && searchQuery?.mode === AppMode.Preview;

  // ? true
  // : userInfo?.info?.id === courseInfo.created_by && searchQuery?.mode === AppMode.Preview;

  const selectedText = useSelector((state: RootState) => state.learner.selectedText);
  const selectedHighlightHyper = useSelector((state: RootState) => state.learner.selectedHighlightHyper);
  const slides = useSelector((state: RootState) => state.learner.slides);
  const previewHyper = useSelector((state: RootState) => state.learner.hyper);

  const isRegisteredCourse = !isLearnerPreview;

  const { isFullScreen } = useFullScreen();

  const [isShowNote, setIsShowNote] = useState<boolean>(false);
  const [tabActive] = useState<string>('1');
  const [isShowNotePopup, setIsShowNotePopup] = useState<boolean>(false);
  const [filterValue, setFilterValue] = useState<number>(1);
  const [isShowEditNoteModal, setIsShowEditNoteModal] = useState<boolean>(false);
  const [isShowDeleteNoteModal, setIsShowDeleteNoteModal] = useState<boolean>(false);
  const [isShowCreatNoteDialog, setIsShowCreatNoteDialog] = useState<boolean>(false);
  const [interactionType, setInteractionType] = useState<LearnerInteractionType>(null);
  const [hyperValue, setHyperValue] = useState<HyperNoteBody | null>(null);
  const [isShowViewNoteDialog, setIsShowViewNoteDialog] = useState<boolean>(false);

  const [paramHyperNote, setParamHyperNote] = useState<HyperNoteParams>({
    ...defaultQuery,
    limit: 50,
    course_id: courseInfo.id!,
    sort_direct: 'desc',
    type_id: +tabActive,
  });

  const { selectedInteractionVideo, setInteractionsVideo, setSelectedInteractionVideo, checkStartInteraction } =
    useInteractionVideo();

  const [filteredNoteValue, setFilteredNoteValue] = useState<string>('');
  const [currentSlideIdx, setCurrentSlideIdx] = useState<number>(0);
  const [selectedLectureDocument, setSelectedLectureDocument] = useState<LectureDocument | null>(null);

  const keyWordFilter = useDebounce(filteredNoteValue, 250);
  const { data: questionData, refetch: refetchQuestion } = useQuery(
    ['getQuestions', paramHyperNote, tabActive, isShowNote, keyWordFilter],
    () => getHyperNotes({ ...paramHyperNote, type_id: 2, key_search: keyWordFilter }),
    {
      enabled: isRegisteredCourse,
    },
  );

  const { data: hyperNoteData, refetch: refetchHyperNote } = useQuery(
    ['getHyperNotes', paramHyperNote, tabActive, isShowNote, keyWordFilter],
    () => getHyperNotes({ ...paramHyperNote, type_id: 1, key_search: keyWordFilter }),
    {
      enabled: isRegisteredCourse,
    },
  );
  const hypers = hyperNoteData?.data.list ?? [];
  const questions = questionData?.data.list ?? [];

  const { mutate: processCourseMutation } = useMutation(
    (body: { courseId: string; sectionId: string; lectureId: string; isCompleted: boolean }) => processCourse(body),
  );

  const { mutate: createHyperNoteMutation, isLoading: createLoading } = useMutation((body: HyperNoteBody) =>
    createHyperNote(body),
  );

  const { mutate: updateHyperNoteMutation, isLoading: updateLoading } = useMutation(
    (body: { title: string; id: number }) => updateHyperNote(body),
  );
  const { mutate: pinnedHyperNoteMutation } = useMutation((body: { is_pinned: number; id: number }) =>
    updatePinnedHyperNote(body),
  );
  const { mutate: deleteHyperNoteMutation, isLoading: deleteLoading } = useMutation((id: number) =>
    deleteHyperNote(id),
  );

  const { mutate: mutateSlideItems } = useMutation({
    mutationFn: updateSlideItems,
  });

  const { playerProgress, setPlayerProgress, videoRef } = useVideoContext();

  const fullScreenRef = useRef(null);

  const isQuestion = interactionType === 'question';
  const currentTime = playerProgress.currentTime ?? 0;

  const typeContent: LectureType | undefined = useMemo(() => {
    if (currentSection?.sectionTypeId === SectionType.Default) {
      return selectedLecture?.lectureTypeId;
    }
    return undefined;
  }, [selectedLecture?.lectureTypeId, currentSection?.sectionTypeId]);

  useEffect(() => {
    if (!selectedLecture?.slide?.slideItems) return;

    const lastSlideItem = selectedLecture?.slide?.slideItems.length - 1;

    if (lastSlideItem === currentSlideIdx) {
      processCourseMutation({
        courseId: courseInfo.id,
        sectionId: sectionId!,
        lectureId: selectedLecture?.id?.toString() || '',
        isCompleted: true,
      });
    }
  }, [currentSlideIdx]);

  useEffect(() => {
    if (
      playerProgress?.currentTime > 0 &&
      playerProgress.duration > 0 &&
      selectedLecture?.lectureTypeId === LectureType.Video &&
      playerProgress?.currentTime === playerProgress.duration
    ) {
      const payload = {
        courseId: courseInfo.id,
        sectionId: sectionId!,
        lectureId: selectedLecture.id?.toString(),
        isCompleted: true,
      };
      processCourseMutation(payload, {
        onSuccess: () => {
          handleNextLecture();
        },
        onError: (error) => {
          console.error('error: ', error);
        },
      });
    }
  }, [playerProgress?.currentTime]);

  useEffect(() => {
    if (searchQuery.source === SOURCE_URL.PREVIEW_HYPER && previewHyper) {
      handleFlyToNote(previewHyper);
    }
  }, []);

  const selectSlide = () => {
    const idx = getIndexSlideById(+slideId!, selectedLecture?.slide?.slideItems);
    if (idx !== -1) {
      setCurrentSlideIdx(idx);
    }
  };

  useEffect(() => {
    // khi chuyển từ creator => learner
    if (!selectedLecture) return;
    if (isPreviewMode) {
      selectSlide();
    }
  }, [isPreviewMode, selectedLecture]);

  useEffect(() => {
    if (!selectedLecture?.id || (isLearnerPreview && !isPreviewMode)) return;
    handleSetInteractionsForVideo();
  }, [isLearnerPreview, isPreviewMode, selectedLecture]);

  useEffect(() => {
    if (isShowViewNoteDialog || selectedHighlightHyper) {
      setIsShowNotePopup(false);
    }
  }, [isShowViewNoteDialog, selectedHighlightHyper]);

  useEffect(() => {
    if (isShowCreatNoteDialog || isShowEditNoteModal || isShowDeleteNoteModal) {
      dispatch(setIsInteractionWithHyper(true));
    } else {
      dispatch(setIsInteractionWithHyper(false));
    }
  }, [isShowCreatNoteDialog, isShowEditNoteModal, isShowDeleteNoteModal]);

  useEffect(() => {
    if (!selectedLecture) return;
    if (selectedLecture?.lectureTypeId === LectureType.Video) {
      checkStartInteraction(currentTime);
    }
  }, [currentTime, selectedLecture]);

  const handleSetInteractionsForVideo = () => {
    const lectureInteracts = selectedLecture?.lectureInteracts;
    const newInteractions = lectureInteracts
      ? lectureInteracts?.map((interaction) => ({
          ...interaction,
          isShowed: !!interaction?.isShowed,
        }))
      : [];
    setInteractionsVideo(newInteractions);
  };

  const currentSegment = useMemo(() => {
    if (!selectedLecture || currentTime <= 0) return;
    const findSegmentByCurrentTime = (currentTime: number) => {
      const totalTimeVideo = playerProgress.duration;
      const currentPercent = (currentTime * 100) / totalTimeVideo;
      return selectedLecture?.lecture_segments?.find(
        (segment: LectureSegment) => currentPercent >= segment.start_at && currentPercent <= segment.end_at,
      );
    };
    return findSegmentByCurrentTime(currentTime);
  }, [selectedLecture, currentTime, playerProgress.duration]);

  const handleShowLectureDocumentForVideo = useCallback(
    (selectedLecture: LectureDetail) => {
      const { lecture_documents, video } = selectedLecture;
      const totalTimeVideo = video?.fileDuration ?? 0;
      const currentLectureDocument = lecture_documents?.find((lectureDocument: LectureDocument) => {
        const startSecond = (lectureDocument.start_at * totalTimeVideo) / 100;
        const endSecond = (lectureDocument.end_at * totalTimeVideo) / 100;
        return currentTime >= startSecond && currentTime < endSecond;
      });
      setSelectedLectureDocument(currentLectureDocument ?? null);
    },
    [currentTime],
  );

  useEffect(
    () => {
      //   handle show note for video
      if (!selectedLecture) return;
      handleShowLectureDocumentForVideo(selectedLecture);
    },
    // [processVideo]
    [handleShowLectureDocumentForVideo, selectedLecture],
  );

  const { handleNextLecture, onClickLectureSidebar } = useNextLecture({
    sectionId: sectionId!,
    courseInfo,
    isPreviewMode,
    selectedLecture,
    setIsReviewCourse,
  });

  const handleShowNote = () => {
    setIsShowNote(!isShowNote);
  };

  const handleAddNote = () => {
    setIsShowCreatNoteDialog(true);
    setInteractionType('question');
  };

  const handleSetCurrentSlideIdx = (hyper: HyperNoteBody) => {
    const slideId = hyper?.extra_info?.slide.slide_id;
    if (slideId) {
      const idx = getIndexSlideById(slideId, selectedLecture?.slide?.slideItems);
      if (idx !== -1) {
        setCurrentSlideIdx(idx);
      }
    }
  };

  const handleFlyToAppearanceTime = (hyper: HyperNoteBody, lectureId: number) => {
    if (!courseInfo) return;
    const currentLecture = getLectureInCourse(courseInfo, sectionId!, lectureId?.toString());

    if (!currentLecture || currentLecture?.lectureTypeId !== LectureType.Video || !hyper.extra_info?.current_time)
      return;

    const appearanceTime = hyper.extra_info?.current_time;
    videoRef.current?.seekTo(appearanceTime);
  };

  const handleFlyToNote = (hyper: HyperNoteBody) => {
    if (lectureId) {
      onClickLectureSidebar({ lectureId: hyper?.lecture_id as number });
      handleFlyToAppearanceTime(hyper, hyper?.lecture_id as number);
    }
    setHyperValue(hyper);
    setIsShowNotePopup(true);
    handleSetCurrentSlideIdx(hyper);
  };

  const closeHyperDialog = () => {
    setIsShowNotePopup(false);
    setIsShowViewNoteDialog(false);
  };

  const handleShowEditAndCloseDrawer = () => {
    closeHyperDialog();
    setIsShowEditNoteModal(true);
  };

  const handleShowDeleteAndCloseDrawer = () => {
    closeHyperDialog();
    setIsShowDeleteNoteModal(true);
  };

  const handleSetModeFilter = (value: number) => {
    setFilterValue(value);
    setParamHyperNote({ ...paramHyperNote, sort_direct: value === 1 ? 'desc' : 'asc' });
  };

  const refetchNoteApi = async () => {
    await refetchHyperNote();
    await refetchQuestion();
  };

  const handleUpdateSlides = () => {
    const body: UpdateSlides = {
      slide_id: selectedLecture?.slide?.id || '',
      data_update: slides as any,
    };
    mutateSlideItems(body, {
      onSuccess: () => {
        dispatch(setSlides([]));
      },
    });
  };

  const handleCreatNewNote = (hyperValue: HyperNoteBody) => {
    const selectedSlide = getSlideInLecture(selectedLecture, currentSlideIdx);
    if (validateLearner.isMaxHypernote(MAX_HYPERNOTE, hypers)) return;

    const body = getBodyCreateNote({
      hyperValue,
      selectedLecture,
      courseInfo,
      selectedSlide,
      sectionId,
      selectedText,
      currentTime,
      lectureId,
      currentSlideIdx,
    } as any);
    // createHyperNoteMutation(body, {
    //   onSuccess: () => {
    //     notifyHyperNoteSuccess(hyperValue);
    //     setIsShowCreatNoteDialog(false);
    //     refetchNoteApi();
    //     form.resetFields();
    //     dispatch(resetSelectedText());
    //   },
    // });
  };

  const handlePinnedHyper = (hyper: HyperNoteBody) => {
    if (!hyper.pinned_at) {
      if (!validPinnedHyper(tabActive === '1' ? hypers : questions)) return;
    }

    pinnedHyperNoteMutation(
      { is_pinned: hyper.pinned_at ? 0 : 1, id: hyper.id },
      {
        onSuccess: () => {
          refetchNoteApi();
          setHyperValue(null);
          notifyPinedHyperNoteSuccess(!hyper.pinned_at);
        },
      },
    );
  };

  const handleEditNote = (hyperValue: HyperNoteBody) => {
    updateHyperNoteMutation(
      {
        ...hyperValue,
      },
      {
        onSuccess: () => {
          notifyUpdateHyperNoteSuccess(hyperValue);
          setIsShowEditNoteModal(false);
          refetchNoteApi();
          form.resetFields();
        },
      },
    );
  };

  const onActionNote = (value: number, type: LearnerInteractionType, hyper: HyperNoteBody) => {
    setInteractionType(type);
    setHyperValue(hyper);
    setIsShowViewNoteDialog(false);
    if (value === 1) {
      handlePinnedHyper(hyper);
      setInteractionType(type);
      return;
    }
    if (value === 2) handleFlyToNote(hyper);
    if (value === 3) handleShowEditAndCloseDrawer();
    if (value === 4) handleShowDeleteAndCloseDrawer();
  };

  const handleDeleteNote = () => {
    if (!hyperValue?.id) return;

    deleteHyperNoteMutation(hyperValue.id, {
      onSuccess: () => {
        notifyDeleteHyperNoteSuccess(hyperValue);
        setIsShowDeleteNoteModal(false);
        setHyperValue(null);
        refetchNoteApi();
      },
    });
  };

  const getHyperByLecture = (hypers: HyperNoteBody[]) => {
    return hypers.filter((hyper) => hyper.lecture_id === +lectureId!);
  };

  const onClickNote = (hyper: HyperNoteBody) => {
    if (isShowCreatNoteDialog || isShowEditNoteModal || isShowDeleteNoteModal) {
      setIsShowViewNoteDialog(false);
      return;
    }
    if (hyperValue?.id === hyper.id) {
      setHyperValue(null);
      setIsShowViewNoteDialog(false);
    } else {
      setHyperValue(hyper);
      setIsShowViewNoteDialog(true);
    }
  };

  const onDbClickNote = (hyper: HyperNoteBody) => {
    setHyperValue(hyper);
    handleFlyToNote(hyper);
    if (isShowViewNoteDialog) {
      setIsShowViewNoteDialog(false);
    }
  };

  const noteItems: MenuProps['items'] = isPreviewMode
    ? []
    : [
        {
          label: 'Tạo ghi chú nhanh',
          key: '1',
          icon: <AddNoteIcon />,
          onClick: ({ domEvent }) => {
            domEvent.stopPropagation();
            setIsShowCreatNoteDialog(true);
            setInteractionType('hypernote');
          },
        },
      ];

  const isShowExploreForVideo =
    selectedInteractionVideo && selectedInteractionVideo.interact_type_id === InteractionType.Explore;

  return (
    <Form form={form} layout={'vertical'}>
      <div className={'relative'}>
        <div className="wrap-playing wrap-playing-learner relative overflow-hidden">
          <LearnerMainContent
            lectureType={typeContent}
            testComponent={
              currentSection ? (
                <TestContainer
                  lectureTestData={lectureTestData!}
                  selectedLecture={selectedLecture}
                  courseInfo={courseInfo}
                  currentSection={currentSection}
                />
              ) : (
                <span>Không tìm thấy chương hiện tại</span>
              )
            }
            videoComponent={
              <div
                className={`wrap-playing wrap-playing-learner relative ${
                  !selectedInteractionVideo?.id ? 'overflow-hidden' : ''
                }`}
                ref={fullScreenRef}
              >
                <Suspense>
                  <InteractionVideo
                    showExplore={isShowExploreForVideo}
                    selectedInteractionVideo={selectedInteractionVideo as any}
                    setSelectedInteractionVideo={setSelectedInteractionVideo as any}
                    reactPlayerProps={{
                      url: selectedLecture?.video?.fileUrl,
                      onEnded: handleNextLecture,
                    }}
                    selectedLectureDocument={selectedLectureDocument}
                    noteItems={noteItems}
                    selectedLecture={selectedLecture as any}
                  />
                </Suspense>
                {!selectedInteractionVideo && (
                  <ControlVideo
                    isLearnerPreview={isLearnerPreview && !isPreviewMode}
                    fullScreenRef={fullScreenRef}
                    isFullScreen={isFullScreen}
                    handleShowNote={handleShowNote}
                    currentSegmentName={currentSegment?.segment_name ?? ''}
                    selectedLecture={selectedLecture}
                    hyperData={getHyperByLecture([...hypers, ...questions])}
                    setSelectedInteractionVideo={setSelectedInteractionVideo}
                    isPreviewMode={isPreviewMode}
                  />
                )}
              </div>
            }
            slideComponent={
              <Dropdown menu={{ items: noteItems }} trigger={['contextMenu']} overlayClassName={'context-menu-learner'}>
                <Suspense>
                  <SlideContainer
                    selectedLecture={selectedLecture}
                    isFullScreen={isFullScreen}
                    handleEndLecture={handleNextLecture}
                    handleShowNote={handleShowNote}
                    hyperData={getHyperByLecture([...hypers, ...questions])}
                    totalTime={selectedLecture?.video?.fileDuration ?? 0}
                    setIsShowCreatNoteDialog={setIsShowCreatNoteDialog}
                    setCurrentSlideIdx={setCurrentSlideIdx}
                    currentSlideIdx={currentSlideIdx}
                    isShowNote={isShowNotePopup}
                    handleFlyToNote={handleFlyToNote}
                    isPreviewMode={isPreviewMode}
                    isPreviewLearner={isLearnerPreview}
                  />
                </Suspense>
              </Dropdown>
            }
          />
        </div>
        <LearnerSectionName
          section={currentSection}
          lectureType={typeContent}
          selectedInteractionVideo={selectedInteractionVideo}
        />
        <Suspense>
          <HyperNotePopup
            isShow={isShowNotePopup}
            value={hyperValue}
            setIsShow={setIsShowNotePopup}
            hypers={hypers}
            selectedLecture={selectedLecture as any}
            setHyperValue={setHyperValue}
          />
          <QuestionPopup
            isShow={isShowNotePopup}
            value={hyperValue}
            setIsShow={setIsShowNotePopup}
            questions={questions}
            setHyperValue={setHyperValue}
          />
          <ViewNoteModal
            isShowViewNoteDialog={isShowViewNoteDialog}
            setIsShowViewNoteDialog={setIsShowViewNoteDialog}
            value={hyperValue}
            setHyperValue={setHyperValue}
            handleFlyToNote={(hyper: HyperNoteBody) =>
              onActionNote(2, hyper.type_id === 1 ? 'hypernote' : 'question', hyper)
            }
            handleEditNote={(hyper: HyperNoteBody) =>
              onActionNote(3, hyper.type_id === 1 ? 'hypernote' : 'question', hyper)
            }
            handleDeleteNote={(hyper: HyperNoteBody) =>
              onActionNote(4, hyper.type_id === 1 ? 'hypernote' : 'question', hyper)
            }
          />
        </Suspense>

        {!playerProgress.playing && !selectedInteractionVideo && typeContent === LectureType.Video && (
          <PauseModal
            setPlaying={(playState) => {
              setPlayerProgress({
                ...playerProgress,
                playing: playState,
              });
            }}
          />
        )}

        <Suspense>
          <NoteDrawer
            isShow={isShowNote}
            setIsShow={setIsShowNote}
            handleAddNote={handleAddNote}
            filterComponent={() => (
              <FilterNote
                onModeFilter={handleSetModeFilter}
                form={form}
                filterValue={filterValue}
                onFilterByKeyWord={setFilteredNoteValue}
              />
            )}
          >
            <Suspense>
              <Notes
                onClickNote={onClickNote}
                onDbClickNote={onDbClickNote}
                onActionNote={(value, hyper) => onActionNote(value, 'hypernote', hyper)}
                hypers={hypers}
                selectedLecture={selectedLecture as any}
                hyperValue={hyperValue}
                filteredNoteValue={filteredNoteValue}
              />
            </Suspense>
          </NoteDrawer>
        </Suspense>
        <UpdateNoteModal
          isLoading={updateLoading}
          isShowEditNoteModal={isShowEditNoteModal}
          isQuestion={isQuestion}
          setIsShowEditNoteModal={setIsShowEditNoteModal}
          handleEditNote={handleEditNote}
          form={form}
          hyper={hyperValue!}
        />
        <DeleteNoteModal
          isLoading={deleteLoading}
          isShowDeleteNoteModal={isShowDeleteNoteModal}
          isQuestion={isQuestion}
          setIsShowDeleteNoteModal={setIsShowDeleteNoteModal}
          handleDeleteNote={handleDeleteNote}
        />
        <CreateNoteModal
          isLoading={createLoading}
          isShowCreatNoteDialog={isShowCreatNoteDialog}
          setIsShowCreatNoteDialog={setIsShowCreatNoteDialog}
          handleCreatNewNote={(value: HyperNoteBody) => {
            handleCreatNewNote(value);
            if (slides.length > 0) {
              handleUpdateSlides();
            }
          }}
          form={form}
          tabActive={tabActive}
        />
      </div>
    </Form>
  );
};

export default LearnerContainer;
