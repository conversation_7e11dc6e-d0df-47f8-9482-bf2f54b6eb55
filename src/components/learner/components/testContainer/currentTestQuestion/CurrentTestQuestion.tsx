import { getLearnerAnswerTest, submitTestQuestion } from '@/features/courses/services/client/tests';
import { Question, Section, UserTestAnswerRequest } from '@/features/courses/types/';
import { TestAnswerRequest, UserTestResult } from '@/features/courses/types/test.type';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { RootState } from '@/store/store';
import { notification } from 'antd/lib';
import { answerMapping } from 'constants/index';
import dayjs from 'dayjs';
import CheckedCircle from 'icons/CheckedCircle';
import ErrorCircle from 'icons/ErrorCircle';
import PlayIcon from 'icons/PlayIcon';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { setLearnerTest } from 'reducer/courseReducer/testSlice';
import { QuestionOption, Test } from 'type';
import QuestionOptionItem from '../questionOptionItem';
import { TestConfirmStartPopup } from '../testConfirmStartPopup/testConfirmStartPopup';
import { TestContainerBottom } from '../testContainerBottom/testContainerBottom';

const Video = dynamic(() => import('components/common/Video'), {
  ssr: false,
});

export const CurrentTestQuestion = ({
  test,
  lecture_id,
  currentSection,
  setShowResultTest,
  showResultTest,
  userAnswers,
  setUserAnswers,
}: {
  test: Test;
  lecture_id: any;
  currentSection: Section;
  setShowResultTest: (show: boolean) => void;
  showResultTest: boolean;
  userAnswers: UserTestResult[];
  setUserAnswers: (userAnswers: UserTestResult[]) => void;
}) => {
  const { courseId }: { courseId: string } = useParams();
  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string }>();
  const sectionId = parsedQueryParams?.sectionId || '';

  const dispatch = useDispatch();
  const learnerTest = useSelector((state: RootState) => state.test.learnerTest);
  const { currentIndex, test_id, answers, startAt, isDoingTest } = learnerTest;
  const [userAnsweredIds, setUserAnsweredIds] = useState<number[]>([]);
  const [popupStart, setPopupStart] = useState<boolean>(!!(test?.has_limit_time && !startAt));
  const [objectAnswer, setObjectAnswer] = useState<any>({});

  const question = test?.questions[currentIndex] as Question;
  const questionId = question.id || '';
  const questionOptions = question?.question_options || [];

  const { mutate: userAnswerTestMutation, isLoading: isSubmitting } = useMutation((request: UserTestAnswerRequest) =>
    submitTestQuestion(request),
  );

  const { isLoading, mutate: getLearnerAnswerTestMutation } = useMutation({
    mutationFn: () =>
      getLearnerAnswerTest({ courseId, sectionId, testId: String(test?.id) || '', times: learnerTest.times }),
    onSuccess: (data) => {
      setUserAnswers(data);
      setShowResultTest(true);
    },
  });

  useEffect(() => {
    if (!test.id) return;
    if (userAnswers.length > 0) {
      let temp: any = {};
      let tempAnswers: any = {};
      userAnswers.forEach((item) => {
        temp = {
          ...temp,
          [item.questionId]: item.point,
        };
        tempAnswers = {
          ...tempAnswers,
          [item.questionId]: item.answer,
        };
      });
      setObjectAnswer(temp);
      dispatch(
        setLearnerTest({
          ...learnerTest,
          answers: tempAnswers,
        }),
      );
      return;
    }
    if (test.has_limit_time && !startAt) {
      setPopupStart(true);
      return;
    }
    dispatch(
      setLearnerTest({
        test_id: test.id,
        currentIndex: 0,
        answers: {},
        startAt: dayjs().valueOf(),
        isDoingTest: true,
      }),
    );
  }, [test, userAnswers]);

  const getCorrectAnswers = () => {
    return question.correct_answer
      .sort((a: number, b: number) => a - b)
      .map((correct: number) => answerMapping[correct.toString()])
      .join(', ');
  };

  const onNextQuestion = () => {
    if (currentIndex === test.questions.length - 1) {
      if (isDoingTest) {
        return dispatch(
          setLearnerTest({
            ...learnerTest,
            showPopupConfirmSubmit: true,
          }),
        );
      } else {
        return;
      }
    }
    setUserAnsweredIds([]);
    dispatch(
      setLearnerTest({
        ...learnerTest,
        currentIndex: currentIndex + 1,
      }),
    );
  };

  const handleSubmitSectionTest = (data: TestAnswerRequest[]) => {
    const answerPayload = {
      courseId: courseId || '',
      sectionId: sectionId || '',
      testId: String(test?.id) || '',
      data,
    } satisfies UserTestAnswerRequest;

    userAnswerTestMutation(answerPayload, {
      onSuccess: () => {
        notification.success({
          message: 'Nộp bài kiểm tra thành công',
        });
        getLearnerAnswerTestMutation();
      },
    });
  };

  const handleAnswer = (chooseAnswer: number) => {
    if (userAnswers?.length) return;
    let newAnswerIds: number[];
    if (question.correct_answer.length > 1) {
      if (userAnsweredIds.includes(chooseAnswer)) {
        newAnswerIds = userAnsweredIds.filter((answer) => answer !== chooseAnswer);
      } else newAnswerIds = [...userAnsweredIds, chooseAnswer];
    } else newAnswerIds = [chooseAnswer];
    setUserAnsweredIds(newAnswerIds);
    dispatch(
      setLearnerTest({
        ...learnerTest,
        answers: {
          ...answers,
          ...(questionId ? { [questionId]: newAnswerIds } : {}),
        },
      }),
    );
  };

  const doSubmit = () => {
    // let params: any = { };
    // if (lecture_id) {
    //   params = {
    //     ...params,
    //     lecture_id,
    //   };
    // } else {
    //   params = {
    //     ...params,
    //     section_id: currentSection.id,
    //   };
    // }
    dispatch(
      setLearnerTest({
        ...learnerTest,
        showPopupConfirmSubmit: false,
      }),
    );

    const answerData = Object.keys(answers).map((question_id) => ({
      question_id: question_id,
      answer: answers[question_id],
    })) as TestAnswerRequest[];

    handleSubmitSectionTest(answerData);
  };

  const ImageQuestion = () =>
    question.question_image ? (
      <div className={'inline-block rounded-[12px] bg-[#F2F2F4] p-[10px]'}>
        <Image
          alt={'question'}
          width={120}
          height={120}
          className={'max-h-[120px] max-w-[120px] object-center'}
          src={question.question_image}
        />
      </div>
    ) : null;

  const videoQuestion = useMemo(
    () =>
      question?.video_url ? (
        <div className={'inline-block w-[300px] rounded-[12px] bg-[#F2F2F4] p-[10px]'}>
          <Video urlFile={question.video_url} scaleIcon={0.5} />
        </div>
      ) : null,
    [question?.video_url, popupStart],
  );

  return (
    <>
      <div className="test-container__question-title">{question.question_name}</div>
      <ImageQuestion />
      {videoQuestion}

      <p className="test-container__description">Chọn đáp án đúng</p>
      <div className={'grid grid-cols-2 gap-8'}>
        {questionOptions.map((item: QuestionOption, index: number) => (
          <QuestionOptionItem
            key={`question-${currentIndex}-${index}`}
            unique={`question-${currentIndex}-${index}`}
            userAnswers={answers[questionId] ? answers[questionId] : []}
            correct_answer={question.correct_answer}
            isMultipleAnswer={question.correct_answer.length > 1}
            isCheckAnswer={!!userAnswers?.length}
            index={index}
            question={item}
            handleAnswer={handleAnswer}
            questionLength={questionOptions.length}
          />
        ))}
      </div>
      <ResultCurrentTest
        isDoingTest={isDoingTest}
        userAnswers={userAnswers}
        correctAnswer={getCorrectAnswers()}
        objectAnswer={objectAnswer}
        question={question}
      />
      {isDoingTest ? (
        <div className="mt-4 text-center">
          <button
            type="button"
            className={`button-confirm-select-answer ${answers[questionId]?.length ? 'selected' : ''} `}
            onClick={onNextQuestion}
          >
            Xác nhận đáp án
          </button>
        </div>
      ) : (
        <button type="button" className={`button-confirm-select-answer selected mt-4`} onClick={onNextQuestion}>
          Câu hỏi tiếp theo <PlayIcon />
        </button>
      )}
      {test ? (
        <TestContainerBottom
          test={test}
          userAnswers={userAnswers}
          doSubmit={doSubmit}
          isLoadingSubmit={isSubmitting || isLoading}
          showResultTest={showResultTest}
        />
      ) : (
        <></>
      )}
      {test.has_limit_time && popupStart ? (
        <TestConfirmStartPopup popupStart={popupStart} setPopupStart={setPopupStart} test={test} />
      ) : null}
    </>
  );
};

export function ResultCurrentTest({
  isDoingTest,
  userAnswers,
  correctAnswer,
  objectAnswer,
  question,
}: Readonly<{
  isDoingTest: boolean;
  userAnswers: UserTestResult[] | undefined;
  correctAnswer: string;
  objectAnswer: {
    [key: string]: string;
  };
  question: Question;
}>) {
  if (isDoingTest || !userAnswers || userAnswers?.length <= 0) return <></>;
  const questionId = question.id || '';
  return (
    <div className={'pt-[24px]'}>
      {objectAnswer[questionId] ? (
        <>
          <div className={'flex gap-2 font-bold text-primary'}>
            <CheckedCircle />
            <p>Chính xác. Đáp án đúng là {correctAnswer}</p>
          </div>
          <p className={'px-3 pt-2 text-left'}>{question.reply_right_answer}</p>
        </>
      ) : (
        <>
          <div className={'flex gap-2 font-bold text-red-500'}>
            <ErrorCircle />
            <p>Không chính xác. Đáp án đúng là {correctAnswer}</p>
          </div>
          <p className={'px-3 pt-2 text-left'}>{question.reply_wrong_answer}</p>
        </>
      )}
    </div>
  );
}

export default CurrentTestQuestion;
