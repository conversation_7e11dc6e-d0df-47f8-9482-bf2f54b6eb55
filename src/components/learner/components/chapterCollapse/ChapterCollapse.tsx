'use client';

import { CourseInfo, LectureDetail, Section } from '@/features/courses';
import { cn } from '@/lib/utils';
import { revalidatePath } from 'actions/revalidatePath';
import { Collapse, Space, Tooltip } from 'antd';
import { returnStatusChapter, returnStatusLecture } from 'components/learner/convert';
import { ChapterStatus } from 'components/learner/type';
import { SectionType } from 'config';
import { useQuerySearch } from 'hooks';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import FinalChapterIcon from 'icons/FinalChapterIcon';
import LearnerStudyingIcon from 'icons/LearnerStudyingIcon';
import LearnerUnCheckIcon from 'icons/LearnerUnCheckIcon';
import LearningChapterIcon from 'icons/LearningChapterIcon';
import LectureCheckedIcon from 'icons/LectureCheckedIcon';
import { useRouter } from 'next-nprogress-bar';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import queryString from 'query-string';
import './index.scss';
type ChapterCollapseProps = {
  courseInfo: CourseInfo;
};

export const checkIsLockedSection = (preSection: Section | undefined, courseInfo: CourseInfo) => {
  const completedLectureIds = courseInfo?.userCompletedLecture?.map((lecture) => lecture.id);
  const isCompletedAllLectureInPreSection = preSection?.lectures.every((lecture) =>
    completedLectureIds?.includes(lecture.id),
  );
  if (!preSection) return false;
  return courseInfo.isSequential === 1 && !isCompletedAllLectureInPreSection;
};

export const ChapterCollapse = (props: ChapterCollapseProps) => {
  const { courseInfo } = props;
  const { handleCloseChapterDrawer } = useChapterDrawer();
  const pathname = usePathname();

  const router = useRouter();

  const redirectAndCloseDrawer = (link: string) => {
    revalidatePath(pathname, 'page');
    router.push(link);
    handleCloseChapterDrawer();
  };

  const { lectureId, sectionId } = useQuerySearch();
  const chapterIcons = [
    {
      key: ChapterStatus.Checked,
      icon: <FinalChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
  ];

  const getIconChapter = (status: ChapterStatus) =>
    chapterIcons.find((chapterIcon) => chapterIcon.key === status)?.icon ?? <></>;

  const getIconLecture = (status: ChapterStatus) => {
    const lectureICons = [
      {
        key: ChapterStatus.Checked,
        component: <LectureCheckedIcon />,
      },
      {
        key: ChapterStatus.Studying,
        component: <LearnerStudyingIcon />,
      },
      {
        key: ChapterStatus.Uncheck,
        component: <LearnerUnCheckIcon />,
      },
    ];
    return lectureICons.find((lectureICon) => lectureICon.key === status)?.component;
  };

  const getHeaderSection = (section: Section, courseInfo: CourseInfo, index: number) => {
    const preSection = courseInfo?.sections?.[index - 1];
    const className = checkIsLockedSection(preSection, courseInfo) ? 'opacity-50' : '';
    let link = '';
    //is learning goal, learning outcome or test section then link
    if (section.sectionTypeId !== SectionType.Default) {
      link = queryString.stringifyUrl({
        url: pathname,
        query: {
          sectionId: section.id,
        },
      });
    }
    if (!link) {
      return (
        <Space size={6} className={`${className} w-full`}>
          {getIconChapter(returnStatusChapter(preSection!, section, courseInfo))}
          <p className={`header-section font-bold ${sectionId === section.id ? 'text-primary' : ''}`}>
            {section.sectionName}
          </p>
        </Space>
      );
    }
    return (
      <Link
        className={`w-full ${sectionId === section.id.toString() ? 'text-primary' : 'text-black hover:text-primary'}`}
        href={link}
        onClick={() => redirectAndCloseDrawer(link)}
      >
        <div className={'flex items-center gap-1.5'}>
          {getIconChapter(returnStatusChapter(preSection!, section, courseInfo))}
          <p className={cn(`header-section font-bold`, sectionId === section.id ? 'text-primary' : '')}>
            {section.sectionName}
          </p>
        </div>
      </Link>
    );
  };

  const getHeaderLecture = (lecture: LectureDetail, section: Section, courseInfo: CourseInfo, index: number) => {
    const preSection = courseInfo?.sections?.[index - 1];
    const isLockedLecture = checkIsLockedSection(preSection, courseInfo);
    const lectureLink = queryString.stringifyUrl({
      url: pathname,
      query: {
        sectionId: section.id,
        lectureId: lecture.id,
      },
    });

    const lectureStatus = returnStatusLecture(lecture, courseInfo, lectureId!);
    const iconLecture = getIconLecture(lectureStatus);

    const renderLecture = () => {
      return (
        <Link
          onClick={isLockedLecture ? () => {} : () => redirectAndCloseDrawer(lectureLink)}
          href={isLockedLecture ? '' : lectureLink}
          className={`${
            lecture.id === lectureId
              ? 'bg-primary-50 font-semibold text-primary'
              : 'hover:bg-neutral-50 hover:text-primary'
          } relative flex items-center truncate border-neutral-100 text-sm text-black`}
          style={{
            opacity: isLockedLecture ? '0.5' : '1',
          }}
        >
          {lecture.lectureName && (
            <Space size={15} className={`w-full cursor-pointer items-center px-3`}>
              <div className={'mb-[5px]'}>{iconLecture}</div>
              <p className={'header-section px-2 py-1'}>{lecture.lectureName}</p>
            </Space>
          )}
        </Link>
      );
    };

    return isLockedLecture ? (
      <Tooltip title={`Hoàn thành chương học ${preSection?.sectionName} để mở khóa`}>{renderLecture()}</Tooltip>
    ) : (
      renderLecture()
    );
  };

  return (
    <div className={'collapse-learner overflow-auto'}>
      <Collapse
        defaultActiveKey={[sectionId!]}
        expandIconPosition={'end'}
        bordered={false}
        items={courseInfo?.sections?.map((section, index) => ({
          label: getHeaderSection(section, courseInfo, index),
          key: section.id?.toString(),
          children: section.lectures.map((lecture, idx) => (
            <div key={`lecture-${lecture.id}-${idx}`}>{getHeaderLecture(lecture, section, courseInfo, index)}</div>
          )),
        }))}
      />
    </div>
  );
};

export default ChapterCollapse;
