import { StarIcon } from '@heroicons/react/20/solid';
import Space from 'antd/es/space';
import { StarCourse } from 'components/common/starCourse';
import RenderTime from 'components/home/<USER>';
import StarBorderOutlined from 'icons/StarBorderOutlined';
import { DefaultThumbnailCourse } from 'images';
import Image from 'next/image';
import Link from 'next/link';
import { formatMinutesToHourMinuteString, getLabelCourseLevel } from 'utils/convert/course';
import { routePaths } from '../../config';

export type HomeCourseItemProps = {
  course_thumbnail_image: string;
  avgRate: number;
  course_name: string;
  sections_count: string | number;
  id: any;
  height?: number;
  course_description: string;
  isNew?: boolean;
  isMasterClass?: boolean;
  totalLearner: number | string;
  total_sections?: number;
  duration: number;
  avg_rating: number;
  isShortCourse: boolean;
  isMajor: boolean;
  course_level_id: number;
  course_type_id?: number;
  isAuthor?: boolean;
  authorName?: string;
  total_learner?: number | string;
  onLike: (isFill: boolean) => void;
  topicName?: string;
  isFavorite: boolean;
  isLoggedIn: boolean;
  slug?: string | undefined;
  isLiking: boolean;
  category: string;
  totalRate: number;
  isHiddenLikeIcon?: boolean;
};

export const StarOutlined = () => {
  return (
    <>
      <svg className="hidden">
        <symbol id="star" width="32" height="30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M31.77 11.857H19.74L15.99.5l-3.782 11.357H0l9.885 6.903-3.692 11.21 9.736-7.05 9.796 6.962-3.722-11.18 9.766-6.845z"
            fill="currentColor"
          />
        </symbol>
      </svg>
      <svg viewBox="0 0 32 30" className="size-3 shrink-0">
        <use xlinkHref="#star"></use>
      </svg>
    </>
  );
};

export function HomeCourseItem(props: Readonly<HomeCourseItemProps>) {
  return (
    <div className={'relative h-full'}>
      {props.isLoggedIn && !props.isHiddenLikeIcon && <StarCourse isFill={props.isFavorite} onLike={props.onLike} />}
      <Link
        href={
          // TODO: Need to double check this
          // props.isLoggedIn
          //   ? `${routePaths.profile.children.course.path}/${props.id}`
          //   : `${routePaths.course.index}/${props.slug}-${props.id}`
          `${routePaths.profile.children.course.path}/${props.id}`
        }
        className={'text-black'}
      >
        <div
          className={`h-full rounded-xl border border-ink-400 bg-white hover:border-primary hover:shadow-2xl ${
            props.isShortCourse ? 'min-h-[320px] hover:text-primary' : 'min-h-[395px]'
          } `}
        >
          <Image
            className={'h-[169px] w-full rounded-t-xl object-cover'}
            src={props.course_thumbnail_image || DefaultThumbnailCourse}
            alt={props.course_name}
            // width={'100%'}
            width={500}
            height={props.height ? props.height : 169}
          />
          <div className={'rounded-b-xl bg-white px-[12px] py-4'}>
            <div className="flex justify-between gap-2 pb-2 pt-4">
              <div title={props.category} className="line-clamp-1 text-xs text-orange-400">
                {props.category}
              </div>
              {props.isShortCourse ? (
                <RenderTime duration={props.duration} />
              ) : (
                <Space size={4} className="text-xs" align={'center'}>
                  <div className="mb-[3px] w-3">
                    <StarIcon />
                  </div>
                  <div className="text-ink-black">{props?.avgRate}</div>
                  <div className="text-ink-700">({props?.totalRate})</div>
                </Space>
              )}
            </div>
            {props.isNew && (
              <div className={'absolute left-3 top-3'}>
                <div className={'bg-white px-2 py-1 text-xs font-bold'}>new</div>
              </div>
            )}
            {props.isMasterClass && !props.isNew && (
              <div className={'absolute left-3 top-3'}>
                <div className={'rounded-xl bg-white px-2 py-1 text-xs font-bold'}>Masterclass</div>
              </div>
            )}
            <div className={'line-clamp-2 font-bold hover:text-primary'}>{props.course_name}</div>
            {!props.isShortCourse && (
              <>
                <div
                  className={'line-clamp-3 pt-2 text-xs text-black'}
                  dangerouslySetInnerHTML={{ __html: props.course_description }}
                />
                <p className={'pt-2 text-xs text-ink-600'}>{props.authorName}</p>
                <div className={'flex items-center gap-1 pt-2 text-sm text-ink-600'}>
                  <p>{formatMinutesToHourMinuteString(props.duration ?? 0)}</p>
                  <StarBorderOutlined />
                  <p>{`${props.sections_count} chương`}</p>
                  <StarBorderOutlined />
                  <p>{getLabelCourseLevel(props.course_level_id)}</p>
                </div>
              </>
            )}

            {/*<RatingAndTotalLearner avgRate={props.avg_rating} totalLearner={+props.totalLearner ?? 0} />*/}
            {/*<div className={'hidden md:flex justify-between text-ink-700 text-xs mt-1'}>*/}
            {/*  <div className={'flex items-center gap-1'}>*/}
            {/*    <ClockIcon className={'w-4 h-4'} />*/}
            {/*    {props.duration ? props.duration : 90} phút*/}
            {/*  </div>*/}
            {/*  <div className={'flex items-center gap-1'}>*/}
            {/*    <CourseOutLined width={16} height={16} />*/}
            {/*    {props.sections_count} chương*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        </div>
      </Link>
    </div>
  );
}

export default HomeCourseItem;
