import React from 'react';
import HeaderGuest from 'components/header/HeaderGuest';
import HeaderLogged from 'components/header/HeaderLogged';
import { userServices } from 'services/user.services';
import { Metadata } from 'next';
import Footer from 'components/Footer';
import { AboutContainer, Banner } from 'components/about-us/components';

export const metadata: Metadata = {
  title: 'Giảng dạy với Studify',
  description: 'Trở thành một người hướng dẫn và thay đổi cuộc sống nhiều người, bao gồm cả chính bạn',
  openGraph: {
    title: 'Giảng dạy với Studify',
    description: 'Trở thành một người hướng dẫn và thay đổi cuộc sống nhiều người, bao gồm cả chính bạn',
    images: [
      {
        url: '/images/bg_creator.png',
      },
    ],
  },
};

async function AboutUs() {
  const { isLoggedIn } = await userServices();

  let header = <HeaderGuest />;

  if (isLoggedIn) {
    header = <HeaderLogged />;
  }

  return (
    <div
      // id="about-us"
      className={'pt-6 overflow-x-hidden'}
    >
      <Banner header={header} />
      <div className={'pt-8'}>
        <AboutContainer />
      </div>
      <Footer />
    </div>
  );
}

export default AboutUs;
