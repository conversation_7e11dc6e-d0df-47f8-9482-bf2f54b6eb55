import { NotFound } from '@/components/common/not-found/NotFound';
import {
  getAllCoursesOfUser,
  getCourseTypes,
  getPublicCourseById,
  getTopics,
} from '@/features/courses/services/server';
import Header from 'components/courses/Header';
import OverallContents from 'components/courses/overallContents/OverallContents';
import { CreatorLink } from 'components/creator-link';
import { formatUrlCourseEditSection } from 'components/learner/utils';
import { ContentTab } from 'constants/enum';
import { Metadata } from 'next';
import Link from 'next/link';
import { AppProps } from 'type/appProps';

export const metadata: Metadata = {
  title: 'Chỉnh sửa thông tin khóa học',
};
export default async function EditCourseInfoPage({ params: { courseId } }: Readonly<AppProps>) {
  const items = [
    {
      key: ContentTab.TabInfo.toString(),
      label: <span className={'text-primary'}>1. Thông tin</span>,
    },
    {
      key: ContentTab.TabContent.toString(),
      label: (
        <Link className={'text-neutral-500 hover:text-primary'} href={formatUrlCourseEditSection(courseId)}>
          2. Nội dung
        </Link>
      ),
      disabled: false,
    },
  ];

  const course = await getPublicCourseById({ courseId: courseId.toString() });
  if (!course) return <NotFound />;

  const { data: topics } = await getTopics();

  const { data: packages } = await getCourseTypes();

  const relatedCourses = await getAllCoursesOfUser();

  return (
    <>
      <Header
        tab={
          <div className={'flex items-center gap-6'}>
            {items?.map((item) => (
              <div className={item.disabled ? 'text-[#00000040]' : ''} key={item.key}>
                {item.label}
              </div>
            ))}
          </div>
        }
        actionComponent={<CreatorLink />}
      />
      <main className={'h-[calc(100vh_-_72px)] overflow-auto'}>
        <OverallContents
          topics={topics}
          packages={packages}
          relatedCourses={relatedCourses.data}
          course={course}
          isEdit={true}
        />
      </main>
    </>
  );
}
