import { NotFound } from '@/components/common/not-found/NotFound';
import { getLectureById, getPublicCourseById, getSectionTestDetail } from '@/features/courses/services/server';
import { mapLectureDetail } from '@/features/courses/utils/mapper';
import { LeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { SectionEditLayout } from 'components/courses/CoureContent/SectionEditLayout';
import ShortCourseContent from 'components/courses/CoureContent/components/shortCourseContent';
import StandardCourseContent from 'components/courses/CoureContent/components/standardCourseContent';
import Header from 'components/courses/Header';
import SectionContent from 'components/courses/SectionContent';
import { CreatorLink } from 'components/creator-link';
import { formatUrlCourseEditInfo, formatUrlCourseEditSection } from 'components/learner/utils/function';
import { SectionType } from 'config';
import { ContentTab, CourseType } from 'constants/enum';
import { VideoProvider } from 'context/VideoProvider';
import { Metadata } from 'next';
import Link from 'next/link';
import { AppProps } from 'type/appProps';

export const metadata: Metadata = {
  title: `Thiết kế nội dung khóa học`,
};

export default async function EditSectionRootPage(props: Readonly<AppProps>) {
  const {
    params: { courseId },
    searchParams,
  } = props;
  const { sectionId, edit, lectureId } = searchParams as { sectionId: string; edit: string; lectureId: string };

  const items = [
    {
      key: ContentTab.TabInfo.toString(),
      label: (
        <Link className={'text-neutral-500 hover:text-primary'} href={formatUrlCourseEditInfo(courseId)}>
          1. Thông tin
        </Link>
      ),
      disabled: false,
    },
    {
      key: ContentTab.TabContent.toString(),
      label: <span className={'text-primary'}>2. Nội dung</span>,
    },
  ];

  const course = await getPublicCourseById({ courseId: courseId.toString() });
  if (!course) return <NotFound />;

  const section = course.sections?.find((section) => section.id === sectionId);

  const isAddNewLayout = !lectureId && section?.sectionTypeId === SectionType.Default;

  const lectureDetail = lectureId
    ? await getLectureById({
        courseId: courseId.toString(),
        sectionId: sectionId,
        lectureId: lectureId,
      })
    : null;

  const lectureDetailAfterMapping = lectureDetail ? mapLectureDetail(lectureDetail) : null;

  const testDetail = section?.test?.id
    ? await getSectionTestDetail({
        courseId: courseId.toString(),
        sectionId: sectionId,
        testId: section?.test?.id || '',
      })
    : null;

  const contents = {
    [CourseType.Standard]: <StandardCourseContent courseInfo={course} />,
    [CourseType.Short]: <ShortCourseContent courseInfo={course} />,
  };

  return (
    <>
      <Header
        tab={
          edit !== 'true' ? (
            <div className={'flex items-center gap-6'}>
              {items?.map((item) => (
                <div className={item.disabled ? 'text-[#00000040]' : ''} key={item.key}>
                  {item.label}
                </div>
              ))}
            </div>
          ) : (
            <></>
          )
        }
        actionComponent={
          edit === 'true' ? (
            <Link
              className={'cursor-pointer text-black hover:text-primary'}
              href={{
                pathname: formatUrlCourseEditSection(course.id!),
              }}
            >
              <Button className={'!px-0'} icon={<LeftOutlined />} type={'link'}>
                {section?.sectionName}
              </Button>
            </Link>
          ) : (
            <CreatorLink />
          )
        }
      />
      <main className={'h-[calc(100vh_-_72px)] overflow-auto'}>
        <VideoProvider>
          <SectionContent isEdit={!!edit} section={section} courseInfo={course}>
            {edit ? (
              <SectionEditLayout
                testDetail={testDetail}
                lecture={lectureDetailAfterMapping}
                courseInfo={course}
                addNewLayout={isAddNewLayout}
                // onPreviewLecture={onPreviewLecture}
                section={section}
                // isLoadingPreview={isLoadingPreview}
              />
            ) : (
              contents[course.courseTypeId as CourseType]
            )}
          </SectionContent>
        </VideoProvider>
      </main>
    </>
  );
}
