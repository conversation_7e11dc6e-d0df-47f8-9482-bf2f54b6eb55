import { getAllCoursesOfUser, getCourseTypes, getTopics } from '@/features/courses/services/server';
import Header from 'components/courses/Header';
import { OverallContents } from 'components/courses/overallContents';
import { CreatorLink } from 'components/creator-link';
import { routePaths } from 'config/constant';
import { ContentTab, CopyrightEnum, CourseLevel, CourseType } from 'constants/enum';
import { Metadata } from 'next';
import Link from 'next/link';

const items = [
  {
    key: ContentTab.TabInfo.toString(),
    label: (
      <Link href={routePaths.course.create} className={'text-primary'}>
        1. Thông tin
      </Link>
    ),
    disabled: false,
  },
  {
    key: ContentTab.TabContent.toString(),
    label: '2. Nội dung',
    disabled: true,
  },
];

export const metadata: Metadata = {
  title: 'Tạo khóa học mới - Thư viện sáng tạ<PERSON>',
};

async function CourseCreatePage() {
  const { data: topics } = await getTopics();

  const { data: packages } = await getCourseTypes();

  const relatedCourses = await getAllCoursesOfUser();

  return (
    <>
      <Header
        actionComponent={<CreatorLink />}
        tab={
          <div className={'hidden items-center gap-6 md:flex'}>
            {items?.map((item) => (
              <div className={item.disabled ? 'text-[#00000040]' : ''} key={item.key}>
                {item.label}
              </div>
            ))}
          </div>
        }
      />
      <main className={'h-[calc(100vh_-_72px)] overflow-auto'}>
        <OverallContents
          topics={topics}
          packages={packages}
          relatedCourses={relatedCourses?.data}
          course={
            {
              is_sequential: 0,
              course_type_id: CourseType.Standard,
              package_id: packages[0].id,
              copyright: CopyrightEnum.Individual,
              course_level_id: CourseLevel.Basic,
            } as any
          }
        />
      </main>
    </>
  );
}

export default CourseCreatePage;
