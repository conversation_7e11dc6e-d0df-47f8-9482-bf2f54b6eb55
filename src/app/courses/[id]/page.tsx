import NotFoundCourse from '@/components/not-found-course';
import { getPublicCourseById } from '@/features/courses/services/server';
import { CourseDetail } from 'components/courses/courseDetail';
import { AppProps } from 'type/appProps';

async function CourseDetailPage({ params }: Readonly<AppProps>) {
  const course = await getPublicCourseById(params.id);

  // TODO: need to check active and publish. This code is commented to integrated API smoothly
  // if (!course || course.active !== CourseActive.Active || course.publish !== CourseActive.Active) {
  //   return <NotFoundCourse />;
  // }

  if (!course) {
    return <NotFoundCourse />;
  }

  return <CourseDetail courseInfo={course} />;
}

export default CourseDetailPage;
