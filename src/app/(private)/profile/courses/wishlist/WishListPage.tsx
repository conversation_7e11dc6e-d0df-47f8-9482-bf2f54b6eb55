'use client';
import { CourseInfo, CourseListBase, useFavoriteCourse } from '@/features/courses';
import { useFavoriteCreator } from '@/features/courses/hooks';
import { useQuerySearch } from '@/hooks';
import { Col, Pagination, Row } from 'antd';
import DiscussTabs from 'components/discuss/DiscussTabs';
import HomeCourseItem from 'components/home/<USER>';
import { FavoriteCreator } from 'components/wishList/components';
import { routePaths } from 'config/constant';
import { CourseType } from 'constants/enum';
import { useRouter } from 'next-nprogress-bar';
import queryString from 'query-string';
import { useContext } from 'react';
import { UserInfo } from 'type';
import UserInfoContext from 'utils/providers/UserInfoProvider';
import './index.scss';

enum TAB {
  COURSE = 'courses',
  AUTHOR = 'authors',
}

const initialLimit = 12;
const initialPage = 1;

function WishListPage(
  props: Readonly<{
    favoriteCoursesData: CourseListBase<CourseInfo>;
    favoriteCreatorsData: CourseListBase<UserInfo>;
  }>,
) {
  const { favoriteCoursesData, favoriteCreatorsData } = props;
  const { data: courses, count: totalCourses, limit: limitCourses = 12 } = favoriteCoursesData;

  const favoriteCreators = favoriteCreatorsData?.data ?? [];

  const queryParams = useQuerySearch();

  const router = useRouter();
  const { isLoggedIn } = useContext(UserInfoContext);

  const { onFavorite, isUpdatingFavorite } = useFavoriteCourse();

  const { onFavorite: onFavoriteCreator } = useFavoriteCreator();

  const onChangePage = (tab: string, page: number, pageSize: number) => {
    router.push(
      queryString.stringifyUrl({
        url: routePaths.profile.children.course.children.wishlist.path,
        query: { ...queryParams, page: page, limit: pageSize, tab },
      }),
    );
  };

  const items = [
    {
      key: TAB.COURSE,
      label: 'Khoá học yêu thích',
      children: (
        <div className={'px-3 pt-6'}>
          <Row gutter={[24, 24]}>
            {courses?.map((item) => {
              return (
                <Col span={24} md={8} lg={6} key={item.id}>
                  <HomeCourseItem
                    authorName={item.createdBy?.name}
                    category={item.topic?.topicName ?? ''}
                    totalRate={Number(item.totalRating) ?? 0}
                    isLiking={isUpdatingFavorite}
                    // slug={item.slug}
                    course_description={item.courseDescription}
                    avg_rating={Number(item.avgRating) ?? 0}
                    duration={Number(item.courseDuration) ?? 0}
                    totalLearner={item.totalLearner ?? 0}
                    course_thumbnail_image={item.courseThumbnailImage ?? ''}
                    avgRate={4}
                    isMajor
                    isAuthor
                    isShortCourse={item.courseTypeId === CourseType.Short}
                    course_name={item.courseName}
                    sections_count={item.totalSections ?? 0}
                    id={item.id}
                    course_level_id={Number(item.courseLevelId)}
                    onLike={(isFavorite: boolean) => onFavorite({ courseId: item.id, isFavorite })}
                    topicName={item.topic?.topicName}
                    total_learner={item.totalLearner ?? 0}
                    isFavorite
                    isLoggedIn={isLoggedIn}
                  />
                </Col>
              );
            })}
          </Row>
          {totalCourses && totalCourses > 0 ? (
            <div className={'center-x-y pt-6'}>
              <Pagination
                total={totalCourses}
                pageSize={limitCourses}
                showSizeChanger={false}
                current={queryParams.page ?? 1}
                onChange={(page: number, pageSize: number) => onChangePage(TAB.COURSE, page, pageSize)}
              />
            </div>
          ) : (
            <p className={'mt-4 text-center font-semibold'}>Chưa có khóa học yêu thích</p>
          )}
        </div>
      ),
    },
    {
      key: TAB.AUTHOR,
      label: 'Creator yêu thích',
      children: (
        <FavoriteCreator
          onClick={({ isFavorite, authorId }) => {
            onFavoriteCreator({ authorId: authorId, isFavorite });
          }}
          queryParams={queryParams}
          favoriteCreators={favoriteCreators}
          onChangePage={(page: number, pageSize: number) => onChangePage(TAB.AUTHOR, page, pageSize)}
        />
      ),
    },
  ];

  return (
    <div className={'white-list px-3'}>
      <div className={'text-2xl font-semibold'}>Wish list</div>
      <DiscussTabs
        items={items}
        defaultActiveKey={queryParams.tab ?? TAB.COURSE}
        onChange={(tab) => {
          onChangePage(tab, initialPage, initialLimit);
        }}
      />
    </div>
  );
}

export default WishListPage;
