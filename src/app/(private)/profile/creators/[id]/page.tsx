import { getCoursesOfCreator } from '@/features/courses/services/server';
import { courseServices } from '@/services/course.services';
import CreatorPage from 'components/creator-page/CreatorPage';
import { defaultGetCourse } from 'constants/config';
import { Metadata, ResolvingMetadata } from 'next';
import { userServices } from 'services/user.services';
import { AppProps } from 'type/appProps';

const ALL_TOPIC = '0';

export async function generateMetadata(props: AppProps, parent: ResolvingMetadata): Promise<Metadata> {
  const courses = await courseServices().getPublishedCourses({
    ...defaultGetCourse,
    key_search: '',
    topic_id: ALL_TOPIC,
    user_id: props.params.id,
  });
  if (courses.list.length <= 0)
    return {
      title: 'Không có khóa học nào',
    };
  const { name, short_introduce, introduce, avatar } = courses.list[0]?.user ?? {};
  return {
    title: name,
    description: short_introduce ?? introduce,
    openGraph: {
      images: [
        {
          url: avatar ?? '',
        },
      ],
    },
  };
}

async function CourseDetailPage({
  params,
}: Readonly<{
  params: {
    id: string;
  };
}>) {
  const { isLoggedIn } = await userServices();

  const coursesOfCreator = await getCoursesOfCreator({ userId: params.id });

  return <CreatorPage courses={coursesOfCreator} isLoggedIn={isLoggedIn} />;
}

export default CourseDetailPage;
