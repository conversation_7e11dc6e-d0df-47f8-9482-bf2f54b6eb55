.hyper-table {
  &,
  table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #e5e5e9 !important;
    border-radius: 10px !important;
    vertical-align: top !important;
  }

  th,
  td {
    text-align: left;
    border: 1px solid #e5e5e9;
    //border-radius: 10px;
    vertical-align: top !important;
  }

  th {
    padding: 10px 10px 10px 60px;
  }
}

.hidden-padding {
  td.ant-table-cell {
    //vertical-align: top !important;
    //padding: 0 !important;
  }
}

.bg-highlight {
  background: #ebebff;
}

.border-hyper {
  border-bottom: 1px solid #e5e5e9;
}
