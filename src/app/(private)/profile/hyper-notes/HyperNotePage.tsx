'use client';
import { CheckCircleIcon } from '@heroicons/react/20/solid';
import { Image, Space, Table, TablePaginationConfig } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import DeleteNoteModal from 'components/learner/components/modals/DeleteNoteModal';
import UpdateNoteModal from 'components/learner/components/modals/UpdateNoteModal';
import { HyperNoteBody } from 'components/learner/type';
import { defaultQuery } from 'constants/query';
import { useDebounce, useHyperNote } from 'hooks';
import { useHyperNoteControl } from 'hooks/homepage/useHyperNote';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import querystring from 'query-string';
import { Suspense, useState } from 'react';
import { useQuery } from 'react-query';
import { FullCourseInfo, Topic, UserInfo } from 'type';
import './index.scss';

const CourseCard = dynamic(() => import('components/hyperContent/CourseCard'));
const TableHyperNote = dynamic(() => import('components/hyperContent/TableHyperNote'));
const ViewHyper = dynamic(() => import('components/hyperContent/ViewHyper'));

export interface DataType {
  id: any;
  key: string;
  name: string;
  status: number;
  category_type: string;
  author: {
    id: any;
    name: string;
    avatar: string;
  };
  total_note: number;
  hyperNotes: HyperNoteBody[];
  courseInfo: FullCourseInfo;
}

function HyperNotePage(
  props: Readonly<{
    coursePaginate: {
      list: FullCourseInfo[];
      total: number;
      limit: number;
      current_page: number;
    };
    topics: Topic[];
  }>,
) {
  const [form] = useForm();

  const { coursePaginate, topics } = props;

  const { list: courses } = coursePaginate;

  const [filteredNoteValue] = useState<string>('');
  const [courseId, setCourseId] = useState<number | null>();
  const [hyperValue, setHyperValue] = useState<HyperNoteBody | null>(null);
  const [isShowEditNoteModal, setIsShowEditNoteModal] = useState<boolean>(false);
  const [isShowDeleteNoteModal, setIsShowDeleteNoteModal] = useState<boolean>(false);
  const [isShowViewNoteDialog, setIsShowViewNoteDialog] = useState<boolean>(false);

  const keyWordFilter = useDebounce(filteredNoteValue, 250);
  const { getHyperNotes } = useHyperNote();

  const { data: hyperNoteData, refetch: refetchHyperNote } = useQuery(
    ['getHyperNotes', courseId],
    () =>
      getHyperNotes({
        ...{
          ...defaultQuery,
          limit: 50,
          course_id: courseId?.toString() || '',
          sort_direct: 'desc',
          type_id: 1,
        },
        key_search: keyWordFilter,
      }),
    { enabled: !!courseId },
  );
  const hypers = hyperNoteData?.data.list ?? [];

  const {
    mappingCourses,
    deleteLoading,
    updateLoading,
    handleEditNote,
    handleFlyToNote,
    handleDeleteNote,
    handlePinnedHyper,
    onSelectHyper,
  } = useHyperNoteControl({
    courses,
    hypers,
    courseId,
    refetchHyperNote,
    setHyperValue,
    setIsShowDeleteNoteModal,
    setIsShowEditNoteModal,
    setIsShowViewNoteDialog,
    topics,
  });

  const handleClickContextMenu = (key: 'pin' | 'remove', hyper: HyperNoteBody) => {
    if (key === 'pin') {
      return handlePinnedHyper(hyper);
    }
    if (key === 'remove') {
      return handleDeleteNote(hyper);
    }
  };

  const columns: ColumnsType<DataType> = [
    {
      title: 'Tên khóa học',
      dataIndex: 'name',
      key: 'name',
      width: 450,
      render: (_, record) => (
        <Suspense>
          <CourseCard course={record.courseInfo} setCourseId={setCourseId} />
        </Suspense>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 160,
      render: (status) => {
        if (status === 1) {
          return (
            <div className={'flex w-[160px] items-center rounded-3xl bg-neutral-50 p-1'}>
              <Space align={'center'} className={'w-full justify-center'}>
                <CheckCircleIcon className={'size-6 text-ink-700'} />
                <div className={'text-sm uppercase'}>Đã hoàn thành</div>
              </Space>
            </div>
          );
        }
        return (
          <div className={'flex w-[150px] items-center rounded-3xl bg-ink-white p-1'}>
            <Space align={'center'} className={'w-full justify-center'}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M10.0001 17.0837C13.9121 17.0837 17.0834 13.9123 17.0834 10.0003C17.0834 6.08831 13.9121 2.91699 10.0001 2.91699C6.08806 2.91699 2.91675 6.08831 2.91675 10.0003C2.91675 13.9123 6.08806 17.0837 10.0001 17.0837ZM10.0001 18.3337C14.6025 18.3337 18.3334 14.6027 18.3334 10.0003C18.3334 5.39795 14.6025 1.66699 10.0001 1.66699C5.39771 1.66699 1.66675 5.39795 1.66675 10.0003C1.66675 14.6027 5.39771 18.3337 10.0001 18.3337Z"
                  fill="#494AFC"
                />
                <path
                  d="M18.3334 10C18.3334 14.6024 14.6025 18.3333 10.0001 18.3333C5.39771 18.3333 1.66675 14.6024 1.66675 10H18.3334Z"
                  fill="#494AFC"
                />
              </svg>
              <div className={'text-sm uppercase text-primary'}>Đang diễn ra</div>
            </Space>
          </div>
        );
      },
    },
    {
      title: 'Thể loại',
      dataIndex: 'category_type',
      key: 'category_type',
    },
    {
      title: 'Tác giả',
      key: 'author',
      dataIndex: 'author',
      render: (author: UserInfo) => (
        <Space>
          <div className={'h-6 w-6'}>
            <Image
              width={24}
              height={24}
              alt={author.name}
              src={author.avatar ?? ''}
              className={'rounded-md object-cover'}
              preview={false}
            />
          </div>
          {author.name}
        </Space>
      ),
    },
    {
      title: 'Số ghi chú',
      key: 'total_note',
      dataIndex: 'total_note',
    },
  ];

  const pathname = usePathname();

  const tablePagination: TablePaginationConfig = {
    total: coursePaginate.total,
    current: coursePaginate.current_page,
    pageSize: coursePaginate.limit,
    itemRender: (page, type, originalElement) => {
      let item = originalElement;

      if (type === 'page') {
        item = page;
      }
      return (
        <Link
          href={querystring.stringifyUrl({
            query: {
              page,
            },
            url: pathname,
          })}
        >
          {item}
        </Link>
      );
    },
  };

  return (
    <>
      {courseId ? (
        <Suspense>
          <TableHyperNote
            hypers={hypers}
            setHyperValue={setHyperValue}
            setCourseId={setCourseId}
            courses={courses}
            courseId={courseId}
            onSelectHyper={onSelectHyper}
            handleClickContextMenu={handleClickContextMenu}
          />
        </Suspense>
      ) : (
        <div className={'relative'}>
          <Table columns={columns} dataSource={mappingCourses} pagination={tablePagination} bordered />
        </div>
      )}

      <UpdateNoteModal
        isLoading={updateLoading}
        isShowEditNoteModal={isShowEditNoteModal}
        isQuestion={false}
        setIsShowEditNoteModal={setIsShowEditNoteModal}
        handleEditNote={handleEditNote}
        form={form}
        hyper={hyperValue!}
      />
      <DeleteNoteModal
        isLoading={deleteLoading}
        isShowDeleteNoteModal={isShowDeleteNoteModal}
        isQuestion={false}
        setIsShowDeleteNoteModal={setIsShowDeleteNoteModal}
        handleDeleteNote={() => handleDeleteNote(hyperValue)}
      />
      <Suspense>
        <ViewHyper
          isShowViewNoteDialog={isShowViewNoteDialog}
          setIsShowViewNoteDialog={setIsShowViewNoteDialog}
          value={hyperValue}
          setHyperValue={setHyperValue}
          handleFlyToNote={(hyper: HyperNoteBody) => handleFlyToNote(hyper)}
          handleEditNote={() => {
            setIsShowEditNoteModal(true);
            setIsShowViewNoteDialog(false);
          }}
          handleDeleteNote={() => {
            setIsShowDeleteNoteModal(true);
            setIsShowViewNoteDialog(false);
          }}
        />
      </Suspense>
    </>
  );
}

export default HyperNotePage;
