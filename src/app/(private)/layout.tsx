import { Metadata } from 'next';
import React from 'react';
import { userServices } from 'services/user.services';

export const metadata: Metadata = {
  title: 'Quản lý profile',
};

export default async function PrivateRootLayout({ children }: { children: React.ReactElement }) {
  const { isLoggedIn } = await userServices();
  console.log('isLoggedIn: ', isLoggedIn);

  // if (!isLoggedIn) {
  //   await removeUserInfoCookie();
  //   redirect(routePaths.login);
  // }

  return children;
}
