import { LectureDetail } from '@/features/courses';
import { getCourseById, getLectureById } from '@/features/courses/services/server';
import { LearnerHeader } from 'components/learner/components/header/LearnerHeader';
import { LearnerContainer } from 'components/learner/components/learnerContainer';
import { LearnerSectionName } from 'components/learner/components/learnerContainer/LearnerSectionName';
import { ResultContainer } from 'components/learner/components/resultContainer';
import { TargetContainer } from 'components/learner/components/targetContainer';
import { TestContainer } from 'components/learner/components/testContainer';
import { NotFoundCourse } from 'components/not-found-course';
import { SectionType, apiUrls, routePaths } from 'config/constant';
import { ChapterSidebarProvider } from 'context/ChapterSidebarProvider';
import { VideoProvider } from 'context/VideoProvider';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { fetchData } from 'services';
import { AppProps } from 'type/appProps';

const ChapterDrawer = dynamic(() => import('components/learner/components/chapterDrawer'), {
  ssr: false,
});
export const metadata: Metadata = {
  title: 'Learner - Studify',
};
const FINISH_PERCENT = 80;

async function LearnerCoursePage({ searchParams, params: { courseId } }: Readonly<AppProps>) {
  const {
    sectionId,
    lectureId,
  }: {
    sectionId?: string;
    lectureId?: string;
  } = searchParams;
  // const courseOfUserPromise = courseServices().getCourseRegistered(courseId);
  // const sectionPromise = sectionService().detail(sectionId!);

  const courseDetail = await getCourseById({ courseId: courseId });

  const section = courseDetail?.sections?.find((section) => section.id === sectionId);

  // if (!courseDetail || !section) return null;

  // const [courseOfUser, section] = await Promise.all([courseOfUserPromise, sectionPromise]);
  // if (!courseOfUser || !section) {
  //   return <NotFoundCourse />;
  // }

  let learnerSection = <></>;

  if (section?.sectionTypeId === SectionType.Test) {
    learnerSection = (
      <div className="wrap-playing wrap-playing-learner relative overflow-hidden">
        <TestContainer courseInfo={courseDetail!} currentSection={section} selectedLecture={undefined} />
        <LearnerSectionName section={section!} lectureType={undefined} />
      </div>
    );
  }

  if (section?.sectionTypeId === SectionType.Target) {
    learnerSection = <TargetContainer courseInfo={courseDetail!} section={section} />;
  }

  if (section?.sectionTypeId === SectionType.Result) {
    learnerSection = <ResultContainer learnerCourse={courseDetail!} currentSection={section} />;
  }

  if (section?.sectionTypeId === SectionType.Default) {
    if (!lectureId) return <NotFoundCourse />;
    // const currentLecture = await lectureService().detail(lectureId);

    const lecture = await getLectureById({ courseId, sectionId: sectionId || '', lectureId });

    const currentLecture = { ...lecture, video: lecture?.videoId } as LectureDetail;
    console.log('currentLecture: ', currentLecture);

    const currentLectureCompleted = courseDetail?.completedLectures?.find(
      (completedLecture) => completedLecture.lecture_id.toString() === lectureId.toString(),
    );

    if (!currentLectureCompleted) {
      await fetchData(`${apiUrls.completedLecture}/${lectureId}`, {
        method: 'POST',
      });
    }

    const completedPercentage = Math.round(
      ((courseDetail?.completedLectures?.length ?? 0) * 100) / (courseDetail?.totalLectures ?? 0),
    );

    const showReviewButton = () => {
      if (courseDetail?.current_user_rated_count && courseDetail.current_user_rated_count > 0) {
        return false;
      }
      return completedPercentage >= FINISH_PERCENT;
    };
    learnerSection = (
      <>
        {showReviewButton() && (
          <Link href={routePaths.learner.children.review.path.replace(':courseId', courseId)}>
            <button
              className={
                'absolute bottom-20 right-10 z-50 rounded-lg bg-primary px-6 py-3 text-white hover:bg-primary-800'
              }
            >
              Đánh giá khóa học
            </button>
          </Link>
        )}
        <VideoProvider>
          <LearnerContainer
            isLearnerPreview={false}
            selectedLecture={currentLecture!}
            courseInfo={courseDetail!}
            currentSection={section}
          />
        </VideoProvider>
      </>
    );
  }

  return (
    <ChapterSidebarProvider>
      <div className={'video-section relative h-screen w-screen overflow-hidden'}>
        <LearnerHeader courseInfo={courseDetail!} />
        {learnerSection}
      </div>
      <ChapterDrawer courseInfo={courseDetail!} />
    </ChapterSidebarProvider>
  );
}

export default LearnerCoursePage;
