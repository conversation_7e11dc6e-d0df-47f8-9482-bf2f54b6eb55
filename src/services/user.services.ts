'use server';

import { getAccessToken, getCookiesAsString, getUserFromCookie } from '@/actions/cookieAction';
import { COOKIE_NAMES } from '@/constants/auth';
import { UserInfo } from '@/type';
import { cookies } from 'next/headers';

export const userServices = async () => {
  const cookieStore = cookies();

  const isLoggedIn = cookieStore.has(COOKIE_NAMES.USER_INFO) && cookieStore.has(COOKIE_NAMES.ACCESS_TOKEN);

  const { userInfoParsed } = await getUserFromCookie();

  const accessToken = await getAccessToken();

  const cookie = await getCookiesAsString();

  const isOnboardedCreator = !!(isLoggedIn && userInfoParsed?.creator_onboarded_at);
  const isOnboardedLearner = !!(isLoggedIn && userInfoParsed?.leaner_onboarded_at);
  const isVipUser = isLoggedIn && userInfoParsed?.is_vip;
  const adminToken = cookieStore.get('admin_token')?.value;

  return {
    cookie,
    accessToken,
    isLoggedIn,
    userInfo: {
      info: userInfoParsed as UserInfo,
      token: accessToken,
    },
    isOnboardedCreator,
    isOnboardedLearner,
    isVipUser,
    adminToken,
  };
};
