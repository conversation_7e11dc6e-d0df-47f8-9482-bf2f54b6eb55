import { API_ENDPOINTS } from '@/constants/api';
import { LectureTestResponse } from '@/features/courses/types';
import { LectureDetail } from '@/features/courses/types/lecture.type';
import { serverFetcher } from '@/lib/serverFetcher';
import { PaginatedQueryBase, UserInfo } from '@/type';
import queryString from 'query-string';
import { CourseListBase } from '../../types/common.type';
import { CourseDetailInfo, CourseInfo, Package, Topic, UserCourse } from '../../types/course.type';
import { CoursesByFilterParams } from '../../types/params.type';

const transformCourseResponse = <T>(res: CourseListBase<T> | null) => {
  if (!res) {
    return { count: 0, data: [], limit: 0, page: 0 };
  }

  const { count, data, limit, page } = res;
  const dataResolved = { count, data, limit, page };
  return dataResolved;
};

export const getTopics = async () => {
  const { data } = await serverFetcher<CourseListBase<Topic>>(API_ENDPOINTS.COURSES.GET.TOPICS);
  const topics = { ...data, data: data?.data || [] };
  return topics;
};

export const getCoursesByFilter = async (payload: CoursesByFilterParams) => {
  const { levels, rate, types, topic } = payload;

  const queryParams = {
    page: 0,
    limit: 12,
    types: types || undefined,
    levels: levels || undefined,
    rate: rate || undefined,
    topic: topic || undefined,
  };

  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.COURSES, query: queryParams });

  const { data: res } = await serverFetcher<CourseListBase<CourseInfo>>(query);

  const data = transformCourseResponse(res);
  return data;
};

export const getNewCourses = async (payload: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.NEW_COURSES,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await serverFetcher<CourseListBase<CourseInfo>>(query);

  const data = transformCourseResponse(res);
  return data;
};

export const getOutstandingCourses = async (payload: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.OUTSTANDING_COURSES,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await serverFetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const getUserCourses = async (payload: Partial<PaginatedQueryBase> & { userId: string }) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.USER_COURSES,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await serverFetcher<CourseListBase<UserCourse>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const getCoursesOfCreator = async (payload: Partial<PaginatedQueryBase> & { userId: string }) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.COURSES_BY_CREATOR.replace(':userId', payload.userId),
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await serverFetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const getCourseById = async (payload: { courseId: string }) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.COURSE_DETAIL.replace(':courseId', payload.courseId),
  });

  const { data: res } = await serverFetcher<CourseDetailInfo>(query);
  return res;
};

export const getCourseTypes = async () => {
  return {
    data: [
      {
        id: '28792266-39a7-4026-952d-1f2ada36e0aa',
        packageName: 'Cơ bản',
        packageDescription: 'Phù hợp cho nội dung học tập cơ bản và có thể không đòi hỏi chuyên môn',
      },
      {
        id: '6ccbcf53-dd4a-4699-98b4-535604468a0d',
        packageName: 'Chuyên nghiệp',
        packageDescription:
          'Phù hợp cho nội dung với chất lượng cao, có thể đòi hỏi chuyên môn và không cung cấp chứng chỉ',
      },
      {
        id: 'a52d032c-5436-454d-9304-7dd4f39953d7',
        packageName: 'Chứng chỉ',
        packageDescription: 'Phù hợp cho nội dung mang tính chất chuyên môn cao và có cung cấp chứng chỉ',
      },
    ] as Package[],
  };
};

export const getAllCoursesOfUser = async (payload?: Partial<PaginatedQueryBase & { publish?: number }>) => {
  const { page = 0, limit = 10, publish } = payload ?? {};
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.ALL_COURSES,
    query: { page, limit, publish },
  });

  const res = await serverFetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse<CourseInfo>(res.data);
  return data;
};

export const getLectureById = async (payload: { courseId: string; sectionId: string; lectureId: string }) => {
  const { courseId, sectionId, lectureId } = payload;

  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.LECTURE_DETAIL.replace(':courseId', courseId)
      .replace(':sectionId', sectionId)
      .replace(':lectureId', lectureId),
  });

  const { data: res } = await serverFetcher<LectureDetail>(query);
  return res;
};

export const getFavoriteCourses = async (payload?: Partial<PaginatedQueryBase>) => {
  const { page = 0, limit = 10 } = payload ?? {};

  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.FAVORITE_COURSES, query: { page, limit } });

  const { data } = await serverFetcher<CourseListBase<CourseInfo>>(query);

  const dataTransformed = transformCourseResponse(data);
  return dataTransformed;
};

export const getFavoriteCreators = async (payload?: Partial<PaginatedQueryBase>) => {
  const { page = 0, limit = 10 } = payload ?? {};

  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.FAVORITE_CREATORS, query: { page, limit } });

  const { data } = await serverFetcher<CourseListBase<UserInfo>>(query);

  const dataTransformed = transformCourseResponse(data);
  return dataTransformed;
};

export const getSectionTestDetail = async (payload: { courseId: string; sectionId: string; testId: string }) => {
  const { courseId, sectionId, testId } = payload;

  const url = API_ENDPOINTS.COURSES.GET.SECTION_TEST_DETAIL.replace(':courseId', courseId)
    .replace(':sectionId', sectionId)
    .replace(':testId', testId);

  const { data } = await serverFetcher<LectureTestResponse>(url);
  return data;
};
