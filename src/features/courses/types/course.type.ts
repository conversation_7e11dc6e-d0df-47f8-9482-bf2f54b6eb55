import { LectureType, SectionType } from '@/config';
import { FileType } from '@/constants/file';
import { LectureInteract, VideoId } from '@/features/courses/types/lecture.type';
import { FileTypeId } from '@/features/courses/utils';
import { LectureDocument, LectureSegment, UserAnswer, UserInfo } from '@/type';
import { Question, Slide } from './slide.type';

export type UserComment = {
  id: string;
  createdAt: string;
  updatedAt: string;
  email: string;
  name: string;
  emailVerifiedAt: string;
  vipEndedAt: string;
  password: string | null;
  phone: string | null;
  avatar: string | null;
  introduce: string | null;
  wardId: number;
  districtId: number;
  provinceId: number;
  totalCourses: number;
  allTotalLearner: number;
  totalCourseCertLearned: number;
  totalCourseProLearned: number;
  totalCourseBasicLearned: number;
  totalFollowers: number;
  shortIntroduce: string | null;
  job: string | null;
  totalMediaSizeUploaded: number;
  roleId: number;
};

export type Comment = {
  id: string;
  updatedBy: string | null;
  user: UserComment;
  parentId: string | null;
  comment: string | null;
  rating: number | null;
  reactions: null;
  active: number;
};

export type CourseFile = {
  file_duration: string | number;
  file_name: string;
  file_size: string | number;
  file_type_id: FileTypeId;
  file_url: string;
  id: string;
};

export type Lecture = {
  id: string;
  lectureTypeId: LectureType;
  testId: string | null;
  videoId: VideoId | null;
  thumbnailFileId: string | null;
  lectureThumbnailImage: string | null;
  lectureName: string;
  slideId: string | null;
  sortIndex: number;
  publish: number;
  createdBy: UserInfo;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;

  section: {
    course: CourseInfo;
    createdBy: string | null;
    id: string;
    learningGoalId: string | null;
    lectures: null;
    publish: number | null;
    sectionDuration: number;
    sectionName: string;
    sectionTypeId: number;
    sortIndex: number;
    testId: string | null;
    updatedAt: string;
    updatedBy: string | null;
  };

  lectureInteracts: LectureInteract[] | null;
  slide: Slide | null;

  //MOCK type
  user_answers?: UserAnswer[];
  sectionId: string;
  test: Test | null;
  lecture_segments: LectureSegment[];
  lecture_documents: LectureDocument[];
  video: VideoId | null;
};

export type Package = {
  id: string;
  packageName: string;
  createdAt: string;
  updatedAt: string;

  // MOCK type
  isVip?: number;
  packageDescription?: string;
};

// TODO: Need to check the TargetItem type with BE
export interface TargetItem {
  title: string;
  description: string;
  sortIndex?: number;
}

// TODO: Need to check the Target type with BE
export interface Target {
  id?: string;
  lecture_id?: string;
  section_id?: string;
  lecture_type_id?: LectureType;
  has_auto_switch: number;
  time_auto_switch: number;
  learning_goal_content: TargetItem[];
  refer_documents: string[];
  study_materials: UploadedFile[];
  lecture_name: string;
  lecture_thumbnail_image: string | null;
  complete_rate_type: number;
  complete_rate_value: number;
  sort_index?: number;
}

export type Topic = {
  id: string;
  topicName: string;
  showOnBoard: number;
  icon: string;
  createdAt: string;
  updatedAt: string;
};

export interface Test {
  id?: string;
  createdAt: string | null;
  updatedAt: string | null;
  testName: string;
  minCorrectAnswer: number;
  hasLimitTime: number | null;
  limitTime: number;
  questions: Question[];
}

export type Section = {
  id: string;
  createdAt: string | null;
  updatedAt: string | null;
  createdBy: string | null;
  updatedBy: string | null;
  sectionName: string;
  sortIndex: number;
  publish: number;
  sectionTypeId: SectionType;
  testId: string | null;
  learningGoalId: string | null;
  sectionDuration: number;
  lectures: Lecture[];
  test: Test | null;

  // MOCK: Mock Type
  learningGoal: Target;
};

export type UserCourse = {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  courseId: string;
  isCompleted: number;
  lastViewLecture: {
    id: string;
    lectureName: string;
    lectureThumbnailImage: string;
    sectionTypeId: number;

    section: {
      id: string;
      sectionName: string;
      sectionDuration: number;
    } & Section; //TODO: Need to double-check
  };
  totalLectures: number;
  countCompletedLectures: number;
  createdAt: string;
  updatedAt: string;
  courses: {
    id: string;
    courseName: string;
    courseDescription: string;
    courseThumbnailImage: string;
    courseLevelId: string;
    courseDuration: string;
    totalSections: string;
    totalLearner: string;
    topic: Topic;
    sections: Section[];
    createdBy: UserInfo;
  };

  totalRating: string | null;
  totalRating5: string | null;
  totalRating4: string | null;
  totalRating3: string | null;
  totalRating2: string | null;
  totalRating1: string | null;
  avgRating: string | null;
};

export interface Tag {
  id: string;
  createdAt: string;
  updatedAt: string;
  tag: {
    id: string;
    tagName: string;
    createdAt: string;
    updatedAt: string;
    updatedBy: string | null;
    createdBy: string | null;
  };
}

export type RelatedCourse = {
  id: string;
  createdAt: string;
  updatedAt: string;
  course: CourseInfo | null;
  related: {
    id: string;
    createdAt: string;
    updatedAt: string;
    courseTypeId: number;
    courseDesignId: number;
    courseName: string;
    courseDescription: string;
    courseThumbnailImage: string;
  };
};

export type CourseInfo = {
  id: string;
  createdAt: string;
  updatedAt: string;
  package: Package;
  courseTypeId: number;
  courseDesignId: string | null;
  courseName: string;
  courseDescription: string;
  courseThumbnailImage: string;
  thumbnailFileId: string | null;
  duration: string | null;
  copyright: string | null;
  topic: Topic;
  courseLevelId: string;
  certificateId: string | null;
  organizationId: string | null;
  isSequential: number;
  sortIndex: string | null;
  publish: number;
  active: number;
  updatedBy: string | null;
  totalRating: string | null;
  totalRating5: string | null;
  totalRating4: string | null;
  totalRating3: string | null;
  totalRating2: string | null;
  totalRating1: string | null;
  avgRating: string | null;
  countFeeling1: string;
  countFeeling2: string;
  countFeeling3: string;
  countFeeling4: string;
  totalLearner: string;
  totalSections: string;
  courseDuration: string;
  createdBy: UserInfo;
  sections: Section[] | null;
  courseRelated: RelatedCourse[];
  courseTag: Tag[];
  isFavorite: boolean;
  // userCompletedLectures?: CompletedLecture[];
  totalLectures?: number;
  countCompletedLectures?: number;
  userCompletedLecture?: CompletedLecture[];

  // TODO: Need to check with BE the these fields below
  current_user_favorite_count: number; // DEPRECATED
  current_user_rated_count?: number;
};

export interface FAQ {
  answer: string;
  createdAt: string;
  id: string;
  question: string;
  updatedAt: string;
}

export type CourseDetailInfo = {
  userCourse: UserCourse;
  courseFaq: FAQ[];
  comments: Comment[];
} & CourseInfo;

export interface CourseLevel {
  id: number;
  courseLevelName: string;
}

export type UploadedFile = {
  countUsed: number;
  createdAt: string;
  fileDuration: number;
  fileName: string;
  fileSize: number;
  fileType: FileType;
  fileUrl: string;
  id: string;
  updatedAt: string;
};

export type LearnerCourseInfo = {
  id: string;
  userId: string;
  courseId: string;
  lastViewLectureId: string | null;
  createdAt: string;
  updatedAt: string;
  completedLectures: {
    id: string;
    user_id: string;
    lecture_id: string;
    course_id: string;
    section_id: string;
    created_at: string;
    updated_at: string;
  }[];
  countCompletedLectures: number;
  totalLectures: number;
  course: CourseInfo;
  isSequential: number;
};

export type CompletedLecture = {
  id: string;
  userId: string;
  lectureId: string;
  courseId: string;
  sectionId: string;
  createdAt: string;
  updatedAt: string;
  lecture: {
    id: string;
    lectureTypeId: LectureType;
    test: Test | null;
    lectureName: string;
    lectureThumbnailImage: string;
    thumbnailFieldId: string | null;
    sortIndex: number;
    publish: number;
    createdAt: string;
    updatedAt: string;
  };
};
