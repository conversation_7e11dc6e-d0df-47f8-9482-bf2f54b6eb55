import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Test } from 'type/course/test';
import { initTest } from './initValueState';

type DefaultState = {
  currentTest: Test;
  tempSubmit: boolean;
  currentCollapse: number;
  learnerTest: any;
};

const initialState: DefaultState = {
  currentTest: initTest,
  tempSubmit: false,
  currentCollapse: -1,
  learnerTest: {
    test_id: null,
    currentIndex: 0,
    answers: {},
    isDoingTest: 0,
    startAt: null,
    showPopupConfirmSubmit: false,
    times: 1,
  },
};

export const testSlice = createSlice({
  name: 'test-management',
  initialState,
  reducers: {
    setCurrentTest: (state: DefaultState, action: PayloadAction<Test>) => {
      state.currentTest = action.payload;
    },
    setTempSubmit: (state: DefaultState, action: PayloadAction<any>) => {
      state.tempSubmit = action.payload;
    },
    setCurrentCollapse: (state: DefaultState, action: PayloadAction<number>) => {
      state.currentCollapse = action.payload;
    },
    setLearnerTest: (state: DefaultState, action: PayloadAction<any>) => {
      state.learnerTest = action.payload;
    },
  },
});

export const { setCurrentTest, setTempSubmit, setCurrentCollapse, setLearnerTest } = testSlice.actions;
export default testSlice.reducer;
