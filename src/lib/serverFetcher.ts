'use server';

import { userServices } from '@/services/user.services';
import https from 'https';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  status: number;
  message: string;
}

interface FetchConfig {
  options?: RequestInit;
  maxRetries?: number;
  retryDelay?: (attempt: number) => number;
}

async function getAuthCredentials() {
  const { cookie } = await userServices();
  if (!cookie) {
    return { cookie: '' };
  }
  return { cookie };
}

function createHeaders(cookie: string, customHeaders?: HeadersInit): HeadersInit {
  return {
    Cookie: cookie,
    ...customHeaders,
  };
}

async function parseResponse<T>(res: Response): Promise<ApiResponse<T>> {
  const status = res.status;
  const contentType = res.headers.get('content-type');

  if (res.ok) {
    const data = contentType?.includes('application/json') ? await res.json() : await res.text();
    return {
      success: true,
      data: data as T,
      status,
      message: 'Request successful',
    };
  }

  if (contentType?.includes('application/json')) {
    try {
      const errorJson = await res.json();
      return {
        success: false,
        data: null,
        status,
        message: errorJson.message || 'An error occurred',
      };
    } catch {
      return {
        success: false,
        data: null,
        status,
        message: 'Failed to parse error response',
      };
    }
  }

  const errorText = await res.text();

  // Try to parse as JSON, but handle cases where it's plain text
  try {
    const errorJsonParsed = JSON.parse(errorText);
    return {
      success: false,
      data: null,
      status,
      message: errorJsonParsed?.message || 'An error occurred',
    };
  } catch {
    // If it's not valid JSON, use the text as the error message
    return {
      success: false,
      data: null,
      status,
      message: errorText || 'An error occurred',
    };
  }
}

export async function serverFetcher<T>(url: string, config: FetchConfig = {}): Promise<ApiResponse<T>> {
  const { options } = config;

  const { cookie } = await getAuthCredentials();

  const agent = process.env.NODE_ENV === 'development' ? new https.Agent({ rejectUnauthorized: false }) : undefined;

  try {
    const res = await fetch(url, {
      ...options,
      headers: createHeaders(cookie, options?.headers),
      ...(agent && { agent }),
      credentials: 'include',
    });

    if (!res.ok) {
      throw { status: res.status, statusText: res.statusText };
    }

    return await parseResponse<T>(res);
  } catch (error) {
    console.error('fetcher error: ', error);
    const errorResponse = error as Response;
    return {
      success: false,
      data: null,
      status: errorResponse.status,
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
