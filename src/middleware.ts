import { HTTP_STATUS_CODE } from '@/constants/api';
import { cookieConstant, routePaths } from 'config';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';
import { COOKIE_NAMES } from './constants/auth';

const prefixPrivateRoutes = routePaths.profile.path;
export function middleware(request: NextRequest, _event: NextFetchEvent) {
  // Clone the request headers and set a new header `x-url`
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-url', request.url);

  const info = request.cookies.get(COOKIE_NAMES.USER_INFO)?.value;
  const accessToken = request.cookies.get(COOKIE_NAMES.ACCESS_TOKEN)?.value;
  const isLoggedIn = info && accessToken;
  const url = request.nextUrl.clone();

  if (request.nextUrl.pathname.includes(prefixPrivateRoutes.replace('/', '')) && !isLoggedIn) {
    url.pathname = routePaths.login;
    const oldPathname = url.pathname;
    const response = NextResponse.redirect(`${url}`);
    response.cookies.set(cookieConstant.callbackAfterLogin, oldPathname);
    return response;
  }

  // You can also set request headers in NextResponse.rewrite
  const response = NextResponse.next({
    request: {
      // New request headers
      headers: requestHeaders,
    },
  });

  console.log('response.status: ', response.status);
  if (response.status === HTTP_STATUS_CODE.UNAUTHORIZED) {
    url.pathname = routePaths.login;
    const res = NextResponse.redirect(url);

    const cookieOptions = { maxAge: 0, domain: '.studify.vn', httpOnly: true };
    [COOKIE_NAMES.USER_INFO, COOKIE_NAMES.ACCESS_TOKEN, COOKIE_NAMES.REFRESH_TOKEN].forEach((cookieName) => {
      res.cookies.set(cookieName, '', cookieOptions);
    });

    return res;
  }

  // Set a new response header `x-hello-from-middleware2`
  response.headers.set('x-hello-from-middleware2', 'hello');
  return response;
}
